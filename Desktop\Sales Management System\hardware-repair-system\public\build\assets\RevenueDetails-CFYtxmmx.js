import{_ as w}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as v}from"./Chart-DPkq-W3M.js";import{m as x,g as i,o as d,a as l,d as p,h as k,w as u,b as e,t as r,n as _,F as m,y as C,l as D}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const B={class:"flex items-center justify-between"},R={class:"flex items-center space-x-3"},j={class:"py-6"},F={class:"max-w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-6"},S={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},A={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},M={class:"flex items-center justify-between"},V={class:"text-2xl font-bold text-white"},L={class:"mt-4 flex items-center"},O={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},z={class:"flex items-center justify-between"},U={class:"text-2xl font-bold text-white"},H={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},I={class:"flex items-center justify-between"},N={class:"text-2xl font-bold text-white"},T={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},$={class:"flex items-center justify-between"},W={class:"text-lg font-bold text-white truncate"},E={class:"text-sm text-gray-400"},G={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},P={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},q={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-xl"},J={class:"overflow-x-auto"},K={class:"min-w-full divide-y divide-gray-700"},Q={class:"bg-gray-800 divide-y divide-gray-700"},X={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},ie={__name:"RevenueDetails",props:{revenueData:{type:Object,default:()=>({})},revenueBreakdown:{type:Array,default:()=>[]},revenueComparison:{type:Array,default:()=>[]},topRevenueServices:{type:Array,default:()=>[]},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({})}},setup(o){const c=o,n=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),b=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,y=s=>s>=0?"text-green-400":"text-red-400",f=x(()=>{const s=c.revenueBreakdown||[];return{labels:s.map(t=>t.service_name||"Unknown"),datasets:[{label:"Revenue by Service",data:s.map(t=>parseFloat(t.revenue||0)),backgroundColor:["rgba(239, 68, 68, 0.8)","rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)"],borderColor:["rgb(239, 68, 68)","rgb(59, 130, 246)","rgb(16, 185, 129)","rgb(245, 158, 11)","rgb(139, 92, 246)","rgb(236, 72, 153)"],borderWidth:2}]}}),h=x(()=>{const s=c.revenueData?.daily_revenue||[];return{labels:s.map(t=>t.date||""),datasets:[{label:"Daily Revenue",data:s.map(t=>parseFloat(t.revenue||0)),borderColor:"rgb(239, 68, 68)",backgroundColor:"rgba(239, 68, 68, 0.1)",tension:.4,fill:!0}]}}),g={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8,callbacks:{label:function(s){let t=s.dataset.label||"";return t&&(t+=": "),s.parsed.y!==null&&(t+="₱"+s.parsed.y.toLocaleString("en-US",{minimumFractionDigits:2})),t}}}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"},callback:function(s){return"₱"+s.toLocaleString("en-US",{minimumFractionDigits:0})}},grid:{color:"#374151",borderColor:"#4B5563"}}}};return(s,t)=>(d(),i(m,null,[l(p(k),{title:"Revenue Details"}),l(w,null,{header:u(()=>[e("div",B,[e("div",null,[e("div",R,[l(p(D),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:u(()=>t[0]||(t[0]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[0]},8,["href"]),t[1]||(t[1]=e("h2",{class:"text-xl font-semibold leading-tight text-white"}," Revenue Details ",-1))]),t[2]||(t[2]=e("p",{class:"text-sm text-gray-400 mt-1"}," Comprehensive revenue analytics and insights ",-1))])])]),default:u(()=>[e("div",j,[e("div",F,[e("div",S,[e("div",A,[e("div",M,[e("div",null,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Revenue",-1)),e("p",V,r(n(o.revenueData.total_revenue)),1)]),t[4]||(t[4]=e("div",{class:"p-3 bg-green-500/20 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))]),e("div",L,[e("span",{class:_([y(o.revenueData.revenue_growth),"text-sm font-medium"])},r(b(o.revenueData.revenue_growth)),3),t[5]||(t[5]=e("span",{class:"text-gray-400 text-sm ml-2"},"vs previous period",-1))])]),e("div",O,[e("div",z,[e("div",null,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-400"},"Avg Daily Revenue",-1)),e("p",U,r(n((o.revenueData.total_revenue||0)/30)),1)]),t[7]||(t[7]=e("div",{class:"p-3 bg-blue-500/20 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",H,[e("div",I,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-400"},"Revenue Sources",-1)),e("p",N,r(o.revenueBreakdown.length),1)]),t[9]||(t[9]=e("div",{class:"p-3 bg-purple-500/20 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),e("div",T,[e("div",$,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-400"},"Top Service",-1)),e("p",W,r(o.revenueBreakdown[0]?.service_name||"N/A"),1),e("p",E,r(n(o.revenueBreakdown[0]?.revenue||0)),1)]),t[11]||(t[11]=e("div",{class:"p-3 bg-yellow-500/20 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1))])])]),e("div",G,[e("div",P,[t[12]||(t[12]=e("div",{class:"flex items-center justify-between mb-6"},[e("h3",{class:"text-lg font-semibold text-white"},"Revenue by Service")],-1)),l(v,{type:"doughnut",data:f.value,options:g,height:300},null,8,["data"])]),e("div",Z,[t[13]||(t[13]=e("div",{class:"flex items-center justify-between mb-6"},[e("h3",{class:"text-lg font-semibold text-white"},"Daily Revenue Trend")],-1)),l(v,{type:"line",data:h.value,options:g,height:300},null,8,["data"])])]),e("div",q,[t[15]||(t[15]=e("div",{class:"px-6 py-4 border-b border-gray-700"},[e("h3",{class:"text-lg font-semibold text-white"},"Revenue Breakdown by Service")],-1)),e("div",J,[e("table",K,[t[14]||(t[14]=e("thead",{class:"bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Service"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Revenue"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Orders"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg Order Value")])],-1)),e("tbody",Q,[(d(!0),i(m,null,C(o.revenueBreakdown,a=>(d(),i("tr",{key:a.service_name,class:"hover:bg-gray-700"},[e("td",X,r(a.service_name),1),e("td",Y,r(n(a.revenue)),1),e("td",ee,r(a.count),1),e("td",te,r(n(a.revenue/a.count)),1)]))),128))])])])])])])]),_:1})],64))}};export{ie as default};
