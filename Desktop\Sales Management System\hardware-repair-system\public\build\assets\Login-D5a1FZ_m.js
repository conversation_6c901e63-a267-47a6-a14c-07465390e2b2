import{u as y,g as l,o,a as u,b as e,d as s,h as w,i,t as n,e as h,j as m,v as g,c as v,k,w as x,f as c,l as b}from"./app-BGh5SFxi.js";import{A as V}from"./ApplicationLogo-CxXkoeMW.js";const _={class:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center px-4"},S={class:"w-full max-w-md"},B={class:"text-center mb-8"},C={class:"mx-auto w-16 h-16 bg-gradient-to-r from-red-600 to-red-700 rounded-xl flex items-center justify-center mb-4 shadow-lg"},P={class:"bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 p-8"},R={key:0,class:"mb-6 p-4 bg-green-900/50 border border-green-700 rounded-lg text-green-300 text-sm"},U={key:0,class:"mt-2 text-sm text-red-400"},j={key:0,class:"mt-2 text-sm text-red-400"},A={class:"flex items-center justify-between"},E={class:"flex items-center"},M=["disabled"],N={key:0,class:"animate-spin w-5 h-5 text-white",fill:"none",viewBox:"0 0 24 24"},T={class:"mt-6 text-center"},F={class:"text-gray-400 text-sm"},D={__name:"Login",props:{canResetPassword:{type:Boolean},status:{type:String}},setup(d){const r=y({email:"",password:"",remember:!1}),f=()=>{r.post(route("login"),{onFinish:()=>r.reset("password")})};return(p,t)=>(o(),l("div",_,[u(s(w),{title:"Log in "}),e("div",S,[e("div",B,[e("div",C,[u(V,{class:"w-8 h-8 text-white"})]),t[3]||(t[3]=e("h1",{class:"text-3xl font-bold text-white mb-2"},"Hardware Repair System",-1)),t[4]||(t[4]=e("p",{class:"text-gray-400"},"Professional repair management solution",-1))]),e("div",P,[d.status?(o(),l("div",R,n(d.status),1)):i("",!0),e("form",{onSubmit:h(f,["prevent"]),class:"space-y-6"},[e("div",null,[t[5]||(t[5]=e("label",{for:"email",class:"block text-sm font-medium text-gray-300 mb-2"}," Email Address ",-1)),m(e("input",{id:"email",type:"email","onUpdate:modelValue":t[0]||(t[0]=a=>s(r).email=a),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Enter your email",required:"",autofocus:"",autocomplete:"username"},null,512),[[g,s(r).email]]),s(r).errors.email?(o(),l("div",U,n(s(r).errors.email),1)):i("",!0)]),e("div",null,[t[6]||(t[6]=e("label",{for:"password",class:"block text-sm font-medium text-gray-300 mb-2"}," Password ",-1)),m(e("input",{id:"password",type:"password","onUpdate:modelValue":t[1]||(t[1]=a=>s(r).password=a),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Enter your password",required:"",autocomplete:"current-password"},null,512),[[g,s(r).password]]),s(r).errors.password?(o(),l("div",j,n(s(r).errors.password),1)):i("",!0)]),e("div",A,[e("label",E,[m(e("input",{type:"checkbox","onUpdate:modelValue":t[2]||(t[2]=a=>s(r).remember=a),class:"w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 focus:ring-2"},null,512),[[k,s(r).remember]]),t[7]||(t[7]=e("span",{class:"ml-2 text-sm text-gray-300"},"Remember me",-1))]),d.canResetPassword?(o(),v(s(b),{key:0,href:p.route("password.request"),class:"text-sm text-red-400 hover:text-red-300 transition-colors duration-200"},{default:x(()=>t[8]||(t[8]=[c(" Forgot password? ",-1)])),_:1,__:[8]},8,["href"])):i("",!0)]),e("button",{type:"submit",disabled:s(r).processing,class:"w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"},[s(r).processing?(o(),l("svg",N,t[9]||(t[9]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),e("span",null,n(s(r).processing?"Signing in...":"Sign In"),1)],8,M)],32),e("div",T,[e("p",F,[t[11]||(t[11]=c(" Don't have an admin account? ",-1)),u(s(b),{href:p.route("register"),class:"text-red-400 hover:text-red-300 font-medium transition-colors duration-200"},{default:x(()=>t[10]||(t[10]=[c(" Register here ",-1)])),_:1,__:[10]},8,["href"])])])]),t[12]||(t[12]=e("div",{class:"mt-8 text-center"},[e("p",{class:"text-gray-500 text-xs"},[e("span",{class:"block mb-1"},"Powered by VSMART TUNE UP"),e("a",{href:"https://web.facebook.com/vinzTSV",target:"_blank",title:"wrench icons",class:"text-gray-400 hover:text-gray-300 transition-colors duration-200"}," Visit Us on Facebook ")])],-1))])]))}};export{D as default};
