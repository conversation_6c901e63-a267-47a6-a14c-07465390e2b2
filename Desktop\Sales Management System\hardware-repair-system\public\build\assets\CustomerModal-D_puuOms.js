import{u as h,r as _,x as C,g as i,i as u,o as a,b as o,e as x,t as l,j as m,v as c,d as s,f as X}from"./app-BGh5SFxi.js";const z={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},V={class:"flex items-center justify-between mb-6"},U={class:"flex items-center space-x-3"},M={class:"text-xl font-bold text-white"},E={class:"text-sm text-gray-400"},N={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},j={key:0,class:"mt-1 text-sm text-red-400"},S={key:0,class:"mt-1 text-sm text-red-400"},B={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},F={key:0,class:"mt-1 text-sm text-red-400"},P={key:0,class:"mt-1 text-sm text-red-400"},q={key:0,class:"mt-1 text-sm text-red-400"},A={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},W={key:0,class:"mt-1 text-sm text-red-400"},D={key:0,class:"mt-1 text-sm text-red-400"},I={key:0,class:"mt-1 text-sm text-red-400"},L={key:0,class:"mt-1 text-sm text-red-400"},T={class:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-700"},H=["disabled"],O={key:0,class:"flex items-center"},Z={key:1},J={__name:"CustomerModal",props:{show:{type:Boolean,default:!1},customer:{type:Object,default:null}},emits:["close","saved"],setup(b,{emit:y}){const d=b,g=y,t=h({first_name:"",last_name:"",facebook_link:"",phone:"",address:"",city:"",state:"",zip_code:"",notes:""}),f=_(!1);C(()=>d.show,n=>{n&&(d.customer?(f.value=!0,t.first_name=d.customer.first_name||"",t.last_name=d.customer.last_name||"",t.facebook_link=d.customer.facebook_link||"",t.phone=d.customer.phone||"",t.address=d.customer.address||"",t.city=d.customer.city||"",t.state=d.customer.state||"",t.zip_code=d.customer.zip_code||"",t.notes=d.customer.notes||""):(f.value=!1,t.reset()))});const w=()=>{if(!t){console.error("Form object is not initialized");return}f.value?t.put(route("customers.update",d.customer.id),{onSuccess:()=>{window.toast&&window.toast.success("Customer updated successfully!"),g("saved"),p()},onError:n=>{console.error("Customer update failed:",n),window.toast&&window.toast.error("Failed to update customer. Please try again.")}}):t.post(route("customers.store"),{onSuccess:()=>{window.toast&&window.toast.success("Customer created successfully!"),g("saved"),p()},onError:n=>{console.error("Customer creation failed:",n),window.toast&&window.toast.error("Failed to create customer. Please try again.")}})},p=()=>{t&&(t.reset(),t.clearErrors()),g("close")},k=n=>{let e=n.replace(/\D/g,"");return e.startsWith("63")?"+"+e:e.startsWith("0")?"+63"+e.substring(1):e.length===10&&e.startsWith("9")?"+63"+e:e.length===9&&e.startsWith("9")?"+639"+e.substring(1):"+63"+e},v=n=>{const e=k(n.target.value);t.phone=e};return(n,e)=>b.show?(a(),i("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:p},[o("div",z,[e[23]||(e[23]=o("div",{class:"fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"},null,-1)),o("div",{class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-2xl rounded-2xl",onClick:e[9]||(e[9]=x(()=>{},["stop"]))},[o("div",V,[o("div",U,[e[10]||(e[10]=o("div",{class:"p-2 bg-red-600 rounded-lg"},[o("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),o("div",null,[o("h3",M,l(f.value?"Edit Customer":"Add New Customer"),1),o("p",E,l(f.value?"Update customer information":"Enter customer details below"),1)])]),o("button",{onClick:p,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},e[11]||(e[11]=[o("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),o("form",{onSubmit:x(w,["prevent"]),class:"space-y-6"},[o("div",N,[o("div",null,[e[12]||(e[12]=o("label",{for:"first_name",class:"block text-sm font-medium text-gray-300 mb-2"}," First Name * ",-1)),m(o("input",{id:"first_name","onUpdate:modelValue":e[0]||(e[0]=r=>s(t).first_name=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Enter first name",required:""},null,512),[[c,s(t).first_name]]),s(t).errors.first_name?(a(),i("div",j,l(s(t).errors.first_name),1)):u("",!0)]),o("div",null,[e[13]||(e[13]=o("label",{for:"last_name",class:"block text-sm font-medium text-gray-300 mb-2"}," Last Name * ",-1)),m(o("input",{id:"last_name","onUpdate:modelValue":e[1]||(e[1]=r=>s(t).last_name=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Enter last name",required:""},null,512),[[c,s(t).last_name]]),s(t).errors.last_name?(a(),i("div",S,l(s(t).errors.last_name),1)):u("",!0)])]),o("div",B,[o("div",null,[e[14]||(e[14]=o("label",{for:"facebook_link",class:"block text-sm font-medium text-gray-300 mb-2"}," Facebook Profile Link * ",-1)),m(o("input",{id:"facebook_link","onUpdate:modelValue":e[2]||(e[2]=r=>s(t).facebook_link=r),type:"url",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"https://facebook.com/username",required:""},null,512),[[c,s(t).facebook_link]]),s(t).errors.facebook_link?(a(),i("div",F,l(s(t).errors.facebook_link),1)):u("",!0)]),o("div",null,[e[15]||(e[15]=o("label",{for:"phone",class:"block text-sm font-medium text-gray-300 mb-2"}," Phone Number * ",-1)),m(o("input",{id:"phone","onUpdate:modelValue":e[3]||(e[3]=r=>s(t).phone=r),onInput:v,type:"tel",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"+63 9XX XXX XXXX",required:""},null,544),[[c,s(t).phone]]),e[16]||(e[16]=o("div",{class:"text-xs text-gray-400 mt-1"},"Format: +63 9XX XXX XXXX",-1)),s(t).errors.phone?(a(),i("div",P,l(s(t).errors.phone),1)):u("",!0)])]),o("div",null,[e[17]||(e[17]=o("label",{for:"address",class:"block text-sm font-medium text-gray-300 mb-2"}," Street Address ",-1)),m(o("input",{id:"address","onUpdate:modelValue":e[4]||(e[4]=r=>s(t).address=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"123 Main Street"},null,512),[[c,s(t).address]]),s(t).errors.address?(a(),i("div",q,l(s(t).errors.address),1)):u("",!0)]),o("div",A,[o("div",null,[e[18]||(e[18]=o("label",{for:"city",class:"block text-sm font-medium text-gray-300 mb-2"}," City ",-1)),m(o("input",{id:"city","onUpdate:modelValue":e[5]||(e[5]=r=>s(t).city=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"City"},null,512),[[c,s(t).city]]),s(t).errors.city?(a(),i("div",W,l(s(t).errors.city),1)):u("",!0)]),o("div",null,[e[19]||(e[19]=o("label",{for:"state",class:"block text-sm font-medium text-gray-300 mb-2"}," State ",-1)),m(o("input",{id:"state","onUpdate:modelValue":e[6]||(e[6]=r=>s(t).state=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"State"},null,512),[[c,s(t).state]]),s(t).errors.state?(a(),i("div",D,l(s(t).errors.state),1)):u("",!0)]),o("div",null,[e[20]||(e[20]=o("label",{for:"zip_code",class:"block text-sm font-medium text-gray-300 mb-2"}," ZIP Code ",-1)),m(o("input",{id:"zip_code","onUpdate:modelValue":e[7]||(e[7]=r=>s(t).zip_code=r),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"12345"},null,512),[[c,s(t).zip_code]]),s(t).errors.zip_code?(a(),i("div",I,l(s(t).errors.zip_code),1)):u("",!0)])]),o("div",null,[e[21]||(e[21]=o("label",{for:"notes",class:"block text-sm font-medium text-gray-300 mb-2"}," Notes ",-1)),m(o("textarea",{id:"notes","onUpdate:modelValue":e[8]||(e[8]=r=>s(t).notes=r),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Additional notes about the customer..."},null,512),[[c,s(t).notes]]),s(t).errors.notes?(a(),i("div",L,l(s(t).errors.notes),1)):u("",!0)]),o("div",T,[o("button",{type:"button",onClick:p,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Cancel "),o("button",{type:"submit",disabled:s(t).processing,class:"px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[s(t).processing?(a(),i("span",O,[e[22]||(e[22]=o("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),X(" "+l(f.value?"Updating...":"Creating..."),1)])):(a(),i("span",Z,l(f.value?"Update Customer":"Create Customer"),1))],8,H)])],32)])])])):u("",!0)}};export{J as _};
