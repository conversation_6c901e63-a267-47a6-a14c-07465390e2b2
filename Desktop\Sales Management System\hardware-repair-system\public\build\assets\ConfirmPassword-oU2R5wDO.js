import{u as m,c as n,o as d,w as t,a as e,b as r,d as o,h as u,e as f,n as p,f as c}from"./app-BGh5SFxi.js";import{_}from"./GuestLayout-_pC6MrRa.js";import{_ as w,a as b,b as x}from"./TextInput-bpP1RZ_5.js";import{P as g}from"./PrimaryButton-Cv9hkgaW.js";import"./ApplicationLogo-CxXkoeMW.js";const y={class:"mt-4 flex justify-end"},k={__name:"ConfirmPassword",setup(P){const s=m({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(V,a)=>(d(),n(_,null,{default:t(()=>[e(o(u),{title:"Confirm Password"}),a[2]||(a[2]=r("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),r("form",{onSubmit:f(i,["prevent"])},[r("div",null,[e(w,{for:"password",value:"Password"}),e(b,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:o(s).password,"onUpdate:modelValue":a[0]||(a[0]=l=>o(s).password=l),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(x,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),r("div",y,[e(g,{class:p(["ms-4",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:t(()=>a[1]||(a[1]=[c(" Confirm ",-1)])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2]}))}};export{k as default};
