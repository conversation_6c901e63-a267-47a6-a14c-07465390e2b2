import{u as f,g as a,o as n,a as u,b as t,d as s,h as x,e as y,j as d,i as l,v as m,t as i,f as p,w as b,l as w}from"./app-BGh5SFxi.js";import{A as v}from"./ApplicationLogo-CxXkoeMW.js";const h={class:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center px-4"},k={class:"w-full max-w-md"},_={class:"text-center mb-8"},A={class:"mx-auto w-16 h-16 bg-gradient-to-r from-red-600 to-red-700 rounded-xl flex items-center justify-center mb-4 shadow-lg"},V={class:"bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 p-8"},C={key:0,class:"mt-2 text-sm text-red-400"},S={key:0,class:"mt-2 text-sm text-red-400"},U={key:0,class:"mt-2 text-sm text-red-400"},N={key:0,class:"mt-2 text-sm text-red-400"},B=["disabled"],E={key:0,class:"animate-spin w-5 h-5 text-white",fill:"none",viewBox:"0 0 24 24"},P={class:"mt-6 text-center"},R={class:"text-gray-400 text-sm"},F={__name:"Register",setup(T){const r=f({name:"",email:"",password:"",password_confirmation:""}),c=()=>{r.post(route("register"),{onFinish:()=>r.reset("password","password_confirmation")})};return(g,e)=>(n(),a("div",h,[u(s(x),{title:"Admin Registration - Hardware Repair System"}),t("div",k,[t("div",_,[t("div",A,[u(v,{class:"w-8 h-8 text-white"})]),e[4]||(e[4]=t("h1",{class:"text-3xl font-bold text-white mb-2"},"Create Admin Account",-1)),e[5]||(e[5]=t("p",{class:"text-gray-400"},"Join the Hardware Repair System",-1))]),t("div",V,[t("form",{onSubmit:y(c,["prevent"]),class:"space-y-6"},[t("div",null,[e[6]||(e[6]=t("label",{for:"name",class:"block text-sm font-medium text-gray-300 mb-2"}," Full Name ",-1)),d(t("input",{id:"name",type:"text","onUpdate:modelValue":e[0]||(e[0]=o=>s(r).name=o),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Enter your full name",required:"",autofocus:"",autocomplete:"name"},null,512),[[m,s(r).name]]),s(r).errors.name?(n(),a("div",C,i(s(r).errors.name),1)):l("",!0)]),t("div",null,[e[7]||(e[7]=t("label",{for:"email",class:"block text-sm font-medium text-gray-300 mb-2"}," Email Address ",-1)),d(t("input",{id:"email",type:"email","onUpdate:modelValue":e[1]||(e[1]=o=>s(r).email=o),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Enter your email",required:"",autocomplete:"username"},null,512),[[m,s(r).email]]),s(r).errors.email?(n(),a("div",S,i(s(r).errors.email),1)):l("",!0)]),t("div",null,[e[8]||(e[8]=t("label",{for:"password",class:"block text-sm font-medium text-gray-300 mb-2"}," Password ",-1)),d(t("input",{id:"password",type:"password","onUpdate:modelValue":e[2]||(e[2]=o=>s(r).password=o),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Create a strong password",required:"",autocomplete:"new-password"},null,512),[[m,s(r).password]]),e[9]||(e[9]=t("div",{class:"mt-2 text-xs text-gray-400"}," Password must be at least 8 characters long ",-1)),s(r).errors.password?(n(),a("div",U,i(s(r).errors.password),1)):l("",!0)]),t("div",null,[e[10]||(e[10]=t("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-300 mb-2"}," Confirm Password ",-1)),d(t("input",{id:"password_confirmation",type:"password","onUpdate:modelValue":e[3]||(e[3]=o=>s(r).password_confirmation=o),class:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200",placeholder:"Confirm your password",required:"",autocomplete:"new-password"},null,512),[[m,s(r).password_confirmation]]),s(r).errors.password_confirmation?(n(),a("div",N,i(s(r).errors.password_confirmation),1)):l("",!0)]),e[12]||(e[12]=t("div",{class:"mb-4 p-3 bg-gray-900/50 border border-gray-700 rounded-lg"},[t("p",{class:"text-xs text-gray-400"}," By creating an account, you agree to our terms of service and privacy policy. Admin accounts have full system access and should be created responsibly. ")],-1)),t("button",{type:"submit",disabled:s(r).processing,class:"w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"},[s(r).processing?(n(),a("svg",E,e[11]||(e[11]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):l("",!0),t("span",null,i(s(r).processing?"Creating Account...":"Create Admin Account"),1)],8,B)],32),t("div",P,[t("p",R,[e[14]||(e[14]=p(" Already have an account? ",-1)),u(s(w),{href:g.route("login"),class:"text-red-400 hover:text-red-300 font-medium transition-colors duration-200"},{default:b(()=>e[13]||(e[13]=[p(" Sign in here ",-1)])),_:1,__:[13]},8,["href"])])])]),e[15]||(e[15]=t("div",{class:"mt-8 text-center"},[t("p",{class:"text-gray-500 text-xs"},[t("span",{class:"block mb-1"},"Powered by VSMART TUNE UP"),t("a",{href:"https://web.facebook.com/vinzTSV",target:"_blank",title:"wrench icons",class:"text-gray-400 hover:text-gray-300 transition-colors duration-200"}," Visit Us on Facebook ")])],-1))])]))}};export{F as default};
