import{u as P,r as w,x as C,g as i,o as d,a as V,d as o,h as D,w as S,b as e,e as N,i as c,j as m,F as g,y as b,t as l,I as y,f,v as h,M as q,n as H}from"./app-BGh5SFxi.js";import{_ as R}from"./AuthenticatedLayout-D7wPPUcl.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const O={class:"p-6"},T={class:"max-w-4xl mx-auto"},E={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},F={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},L=["value"],Y={key:0,class:"mt-2 text-sm text-red-400"},G={key:0,class:"bg-gray-800 rounded-lg p-4 border border-gray-600"},J={class:"space-y-1 text-sm text-gray-400"},K={key:0},Q={key:0,class:"mt-6"},W=["value"],X={key:0,class:"mt-2 text-sm text-red-400"},Z={key:1,class:"mt-2 p-3 bg-yellow-900 border border-yellow-700 rounded-lg"},I={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ee={class:"mb-6"},te={class:"flex items-center justify-between mb-3"},se={class:"text-white font-medium"},re=["onClick"],oe={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},ie=["onUpdate:modelValue","onChange"],de=["value"],le={key:0,class:"mt-2 text-sm text-red-400"},ne=["onUpdate:modelValue"],ae={key:0,class:"mt-2 text-sm text-red-400"},ce={class:"mt-4"},ue=["onUpdate:modelValue"],me={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ge=["value"],ve={key:0,class:"mt-2 text-sm text-red-400"},pe={class:"mt-6"},be={class:"grid grid-cols-2 lg:grid-cols-4 gap-3"},xe=["value","onUpdate:modelValue"],ye={class:"font-medium capitalize"},fe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},he={class:"space-y-6"},_e={key:0,class:"mt-2 text-sm text-red-400"},we={key:0,class:"mt-2 text-sm text-red-400"},ke={class:"flex items-center justify-end space-x-4 pt-6"},Ce=["disabled"],Ve={key:0,class:"flex items-center"},Se={key:1},Ae={__name:"Create",props:{customers:Array,services:Array,technicians:Array},setup(_){const k=_,r=P({customer_id:"",device_id:"",services:[{service_id:"",service_price:"",service_notes:""}],technician_id:"",issue_description:"",priority:"medium",customer_notes:"",estimated_completion:"",selected_parts:[]}),v=w([]),p=w(null),x=w([]),M=async n=>{if(!n){v.value=[];return}try{const t=await fetch(`/customers/${n}/devices`);v.value=await t.json()}catch(t){console.error("Error fetching customer devices:",t),v.value=[]}};C(()=>r.customer_id,n=>{r.device_id="",p.value=k.customers.find(t=>t.id==n),M(n)}),C(()=>r.device_id,n=>{if(n){const t=v.value.find(s=>s.id==n);t&&t.device_type&&(x.value=k.services.filter(s=>s.device_type_id==t.device_type.id))}else x.value=[];r.services=[{service_id:"",service_price:"",service_notes:""}]});const U=()=>{r.services.push({service_id:"",service_price:"",service_notes:""})},j=n=>{r.services.length>1&&r.services.splice(n,1)},z=n=>{const t=x.value.find(s=>s.id==n);return t?t.base_price:0},A=(n,t)=>{t&&!r.services[n].service_price&&(r.services[n].service_price=z(t))},$=()=>{r.post(route("repair-orders.store"))},B=n=>{const t={low:"bg-gray-100 text-gray-800 border-gray-300",medium:"bg-blue-100 text-blue-800 border-blue-300",high:"bg-orange-100 text-orange-800 border-orange-300",urgent:"bg-red-100 text-red-800 border-red-300"};return t[n]||t.medium};return(n,t)=>(d(),i(g,null,[V(o(D),{title:"Create Repair Order"}),V(R,null,{header:S(()=>t[6]||(t[6]=[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h2",{class:"text-2xl font-bold text-white"},"Create Repair Order"),e("p",{class:"text-gray-400 text-sm"},"Start a new repair service request")])],-1)])),default:S(()=>[e("div",O,[e("div",T,[e("form",{onSubmit:N($,["prevent"]),class:"space-y-8"},[e("div",E,[t[16]||(t[16]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Customer Information")],-1)),e("div",F,[e("div",null,[t[8]||(t[8]=e("label",{for:"customer_id",class:"block text-sm font-medium text-gray-300 mb-2"},"Select Customer *",-1)),m(e("select",{id:"customer_id","onUpdate:modelValue":t[0]||(t[0]=s=>o(r).customer_id=s),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},[t[7]||(t[7]=e("option",{value:""},"Choose a customer...",-1)),(d(!0),i(g,null,b(_.customers,s=>(d(),i("option",{key:s.id,value:s.id},l(s.full_name)+" - "+l(s.phone),9,L))),128))],512),[[y,o(r).customer_id]]),o(r).errors.customer_id?(d(),i("div",Y,l(o(r).errors.customer_id),1)):c("",!0)]),p.value?(d(),i("div",G,[t[12]||(t[12]=e("h4",{class:"text-sm font-medium text-gray-300 mb-2"},"Customer Details",-1)),e("div",J,[e("p",null,[t[9]||(t[9]=e("span",{class:"text-gray-300"},"Email:",-1)),f(" "+l(p.value.email),1)]),e("p",null,[t[10]||(t[10]=e("span",{class:"text-gray-300"},"Phone:",-1)),f(" "+l(p.value.phone),1)]),p.value.address?(d(),i("p",K,[t[11]||(t[11]=e("span",{class:"text-gray-300"},"Address:",-1)),f(" "+l(p.value.address),1)])):c("",!0)])])):c("",!0)]),o(r).customer_id?(d(),i("div",Q,[t[15]||(t[15]=e("label",{for:"device_id",class:"block text-sm font-medium text-gray-300 mb-2"},"Select Device *",-1)),m(e("select",{id:"device_id","onUpdate:modelValue":t[1]||(t[1]=s=>o(r).device_id=s),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},[t[13]||(t[13]=e("option",{value:""},"Choose a device...",-1)),(d(!0),i(g,null,b(v.value,s=>(d(),i("option",{key:s.id,value:s.id},l(s.brand)+" "+l(s.model)+" ("+l(s.device_type?.name)+") ",9,W))),128))],512),[[y,o(r).device_id]]),o(r).errors.device_id?(d(),i("div",X,l(o(r).errors.device_id),1)):c("",!0),v.value.length===0&&o(r).customer_id?(d(),i("div",Z,t[14]||(t[14]=[e("p",{class:"text-yellow-300 text-sm"},"No devices found for this customer. You may need to register a device first.",-1)]))):c("",!0)])):c("",!0)]),e("div",I,[t[27]||(t[27]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Service Details")],-1)),e("div",ee,[e("div",{class:"flex items-center justify-between mb-4"},[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-300"},"Services Required *",-1)),e("button",{type:"button",onClick:U,class:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-sm transition-colors duration-200 flex items-center space-x-1"},t[17]||(t[17]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Service",-1)]))]),(d(!0),i(g,null,b(o(r).services,(s,u)=>(d(),i("div",{key:u,class:"mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700"},[e("div",te,[e("h4",se,"Service "+l(u+1),1),o(r).services.length>1?(d(),i("button",{key:0,type:"button",onClick:a=>j(u),class:"text-red-400 hover:text-red-300 transition-colors duration-200"},t[19]||(t[19]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,re)):c("",!0)]),e("div",oe,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Type *",-1)),m(e("select",{"onUpdate:modelValue":a=>s.service_id=a,onChange:a=>A(u,s.service_id),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},[t[20]||(t[20]=e("option",{value:""},"Select service...",-1)),(d(!0),i(g,null,b(x.value,a=>(d(),i("option",{key:a.id,value:a.id},l(a.name)+" - $"+l(a.base_price),9,de))),128))],40,ie),[[y,s.service_id]]),o(r).errors[`services.${u}.service_id`]?(d(),i("div",le,l(o(r).errors[`services.${u}.service_id`]),1)):c("",!0)]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Price",-1)),m(e("input",{type:"number",step:"0.01",min:"0","onUpdate:modelValue":a=>s.service_price=a,class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Custom price (optional)"},null,8,ne),[[h,s.service_price]]),o(r).errors[`services.${u}.service_price`]?(d(),i("div",ae,l(o(r).errors[`services.${u}.service_price`]),1)):c("",!0)])]),e("div",ce,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Notes",-1)),m(e("textarea",{"onUpdate:modelValue":a=>s.service_notes=a,rows:"2",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Any specific notes for this service..."},null,8,ue),[[h,s.service_notes]])])]))),128))]),e("div",me,[e("div",null,[t[25]||(t[25]=e("label",{for:"technician_id",class:"block text-sm font-medium text-gray-300 mb-2"},"Assign Technician",-1)),m(e("select",{id:"technician_id","onUpdate:modelValue":t[2]||(t[2]=s=>o(r).technician_id=s),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},[t[24]||(t[24]=e("option",{value:""},"Assign later...",-1)),(d(!0),i(g,null,b(_.technicians,s=>(d(),i("option",{key:s.id,value:s.id},l(s.user?.name)+" - "+l(s.specialization),9,ge))),128))],512),[[y,o(r).technician_id]]),o(r).errors.technician_id?(d(),i("div",ve,l(o(r).errors.technician_id),1)):c("",!0)])]),e("div",pe,[t[26]||(t[26]=e("label",{for:"priority",class:"block text-sm font-medium text-gray-300 mb-2"},"Priority Level",-1)),e("div",be,[(d(),i(g,null,b(["low","medium","high","urgent"],s=>e("label",{key:s,class:"relative cursor-pointer"},[m(e("input",{type:"radio",value:s,"onUpdate:modelValue":u=>o(r).priority=u,class:"sr-only"},null,8,xe),[[q,o(r).priority]]),e("div",{class:H(["p-3 rounded-lg border-2 text-center transition-all duration-200",o(r).priority===s?B(s):"bg-gray-800 border-gray-600 text-gray-400 hover:border-gray-500"])},[e("div",ye,l(s),1)],2)])),64))])])]),e("div",fe,[t[30]||(t[30]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Problem Description")],-1)),e("div",he,[e("div",null,[t[28]||(t[28]=e("label",{for:"issue_description",class:"block text-sm font-medium text-gray-300 mb-2"},"Describe the Problem *",-1)),m(e("textarea",{id:"issue_description","onUpdate:modelValue":t[3]||(t[3]=s=>o(r).issue_description=s),rows:"4",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Please describe the issue with the device in detail...",required:""},null,512),[[h,o(r).issue_description]]),o(r).errors.issue_description?(d(),i("div",_e,l(o(r).errors.issue_description),1)):c("",!0)]),e("div",null,[t[29]||(t[29]=e("label",{for:"customer_notes",class:"block text-sm font-medium text-gray-300 mb-2"},"Customer Notes",-1)),m(e("textarea",{id:"customer_notes","onUpdate:modelValue":t[4]||(t[4]=s=>o(r).customer_notes=s),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Any additional notes from the customer..."},null,512),[[h,o(r).customer_notes]]),o(r).errors.customer_notes?(d(),i("div",we,l(o(r).errors.customer_notes),1)):c("",!0)])])]),e("div",ke,[e("button",{type:"button",onClick:t[5]||(t[5]=s=>n.$inertia.visit(n.route("repair-orders.index"))),class:"px-6 py-3 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-800 transition-colors duration-200 font-medium"}," Cancel "),e("button",{type:"submit",disabled:o(r).processing,class:"px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[o(r).processing?(d(),i("span",Ve,t[31]||(t[31]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),f(" Creating Order... ",-1)]))):(d(),i("span",Se,"Create Repair Order"))],8,Ce)])],32)])])]),_:1})],64))}};export{Ae as default};
