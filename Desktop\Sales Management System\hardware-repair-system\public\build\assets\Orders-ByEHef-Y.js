import{_ as S}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as m}from"./Chart-DPkq-W3M.js";import{m as x,g as i,o as g,a as n,d as f,h as F,w as c,b as t,t as r,F as y,y as A,n as h,l as L}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const M={class:"flex items-center justify-between"},V={class:"flex items-center space-x-4"},I={class:"flex items-center space-x-3"},R=["value"],U={class:"py-8"},z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},T={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},N={class:"bg-gradient-to-br from-green-600 to-green-700 rounded-xl p-6 text-white shadow-lg"},P={class:"flex items-center justify-between"},W={class:"text-3xl font-bold mt-1"},$={class:"bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-6 text-white shadow-lg"},E={class:"flex items-center justify-between"},H={class:"text-3xl font-bold mt-1"},Y={class:"bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl p-6 text-white shadow-lg"},Z={class:"flex items-center justify-between"},q={class:"text-3xl font-bold mt-1"},G={class:"bg-gradient-to-br from-orange-600 to-orange-700 rounded-xl p-6 text-white shadow-lg"},J={class:"flex items-center justify-between"},K={class:"text-3xl font-bold mt-1"},Q={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},X={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},tt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},et={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-lg"},ot={class:"p-6"},st={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},rt={class:"flex items-center justify-between"},lt={class:"text-sm text-gray-400"},ut={__name:"Orders",props:{data:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(s){const u=s,v=o=>"₱"+parseFloat(o||0).toLocaleString("en-US",{minimumFractionDigits:2}),w=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),p=o=>`${o.toFixed(1)}%`,_=x(()=>{const o=u.charts.daily_trend||[];return{labels:o.map(e=>w(e.date)),datasets:[{label:"Daily Orders",data:o.map(e=>parseInt(e.count||0)),borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",tension:.4,fill:!0}]}}),k=x(()=>{const o=u.data.orders_by_status||{},e={pending:"rgba(245, 158, 11, 0.8)",in_progress:"rgba(59, 130, 246, 0.8)",waiting_parts:"rgba(249, 115, 22, 0.8)",completed:"rgba(16, 185, 129, 0.8)",delivered:"rgba(34, 197, 94, 0.8)",cancelled:"rgba(239, 68, 68, 0.8)"},l=Object.keys(o).map(d=>d.replace("_"," ").toUpperCase()),a=Object.values(o),B=Object.keys(o).map(d=>e[d]||"rgba(156, 163, 175, 0.8)");return{labels:l,datasets:[{data:a,backgroundColor:B,borderWidth:2}]}}),b={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}},C={...b,scales:void 0},D=o=>{window.location.href=route("reports.orders",{period:o})},O=o=>({pending:"text-yellow-400",in_progress:"text-blue-400",waiting_parts:"text-orange-400",completed:"text-green-400",delivered:"text-green-500",cancelled:"text-red-400"})[o]||"text-gray-400",j=o=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",waiting_parts:"bg-orange-100 text-orange-800",completed:"bg-green-100 text-green-800",delivered:"bg-green-200 text-green-900",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800";return(o,e)=>(g(),i(y,null,[n(f(F),{title:"Orders Analytics"}),n(S,null,{header:c(()=>[t("div",M,[t("div",V,[n(f(L),{href:o.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:c(()=>e[1]||(e[1]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),e[2]||(e[2]=t("div",null,[t("h2",{class:"text-2xl font-bold leading-tight text-white"}," Orders Analytics "),t("p",{class:"text-sm text-gray-400 mt-1"}," Detailed order analysis and performance metrics ")],-1))]),t("div",I,[t("select",{value:s.period,onChange:e[0]||(e[0]=l=>D(l.target.value)),class:"bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},e[3]||(e[3]=[t("option",{value:"7days"},"Last 7 Days",-1),t("option",{value:"30days"},"Last 30 Days",-1),t("option",{value:"90days"},"Last 90 Days",-1),t("option",{value:"thisyear"},"This Year",-1)]),40,R)])])]),default:c(()=>[t("div",U,[t("div",z,[t("div",T,[t("div",N,[t("div",P,[t("div",null,[e[4]||(e[4]=t("p",{class:"text-green-100 text-sm font-medium"},"Total Orders",-1)),t("p",W,r(s.data.total_orders?.toLocaleString()),1)]),e[5]||(e[5]=t("div",{class:"p-3 bg-green-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),t("div",$,[t("div",E,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-blue-100 text-sm font-medium"},"Completed Orders",-1)),t("p",H,r(s.data.completed_orders?.toLocaleString()),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-blue-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),t("div",Y,[t("div",Z,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-purple-100 text-sm font-medium"},"Completion Rate",-1)),t("p",q,r(p(s.data.completion_rate)),1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-purple-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1))])]),t("div",G,[t("div",J,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-orange-100 text-sm font-medium"},"Avg. Order Value",-1)),t("p",K,r(v(s.data.average_order_value)),1)]),e[11]||(e[11]=t("div",{class:"p-3 bg-orange-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-orange-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])])]),t("div",Q,[t("div",X,[e[12]||(e[12]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Orders Trend",-1)),n(m,{type:"line",data:_.value,options:b,height:400},null,8,["data"])]),t("div",tt,[e[13]||(e[13]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Orders by Status",-1)),n(m,{type:"doughnut",data:k.value,options:C,height:400},null,8,["data"])])]),t("div",et,[e[14]||(e[14]=t("div",{class:"p-6 border-b border-gray-700"},[t("h3",{class:"text-lg font-semibold text-white"},"Order Status Breakdown")],-1)),t("div",ot,[t("div",st,[(g(!0),i(y,null,A(s.data.orders_by_status,(l,a)=>(g(),i("div",{key:a,class:"bg-gray-700 rounded-lg p-4 border border-gray-600"},[t("div",rt,[t("div",null,[t("span",{class:h(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",j(a)])},r(a.replace("_"," ").toUpperCase()),3),t("p",{class:h(["text-2xl font-bold mt-2",O(a)])},r(l),3),t("p",lt,r(p(l/s.data.total_orders*100))+" of total ",1)])])]))),128))])])])])])]),_:1})],64))}};export{ut as default};
