import{r as i,x as I,q as V,g as n,o as l,a as w,d as c,h as N,w as p,b as e,t as o,i as u,F as k,y as B,f as T,n as z,c as C,l as m,j as M,v as E,I as H}from"./app-BGh5SFxi.js";import{_ as q}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as G}from"./RepairOrderModal-DZH8SLto.js";import{_ as Q}from"./ConfirmationModal-Bx9j458V.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const W={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},J={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},K={class:"relative flex-1 max-w-md"},X={class:"flex items-center space-x-2"},Y=["value"],Z={class:"flex items-center space-x-2"},ee={class:"p-6 space-y-6"},te={class:"grid grid-cols-1 md:grid-cols-5 gap-6"},re={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},se={class:"flex items-center justify-between"},oe={class:"text-2xl font-bold text-white"},le={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ae={class:"flex items-center justify-between"},ne={class:"text-2xl font-bold text-white"},ie={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},de={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-white"},ce={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},pe={class:"flex items-center justify-between"},ge={class:"text-2xl font-bold text-white"},xe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ve={class:"flex items-center justify-between"},me={class:"text-2xl font-bold text-white"},he={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},ye={class:"overflow-x-auto"},fe={class:"min-w-full divide-y divide-gray-800"},be={class:"bg-gray-900 divide-y divide-gray-800"},we={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},_e={class:"text-xs text-gray-500"},Me={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Oe={class:"px-6 py-4 whitespace-nowrap"},je={class:"px-6 py-4 whitespace-nowrap"},Ve={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Be={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Te={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ze={class:"flex items-center space-x-1"},He=["onClick"],Se={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},De={class:"flex items-center justify-between"},Ae={class:"flex-1 flex justify-between sm:hidden"},Pe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Le={class:"text-sm text-gray-400"},Ue={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Fe=["innerHTML"],Qe={__name:"Index",props:{repairOrders:Object,customers:Array,devices:Array,services:Array,technicians:Array,parts:Array,filters:Object},setup(s){const _=s,h=i(!1),O=i(!1),j=i(null),y=i(null),f=i(!1),d=i(_.filters?.search||""),g=i(_.filters?.status||"all"),x=i(_.filters?.priority||"all"),v=i(_.filters?.technician||"all");let S;I(d,a=>{clearTimeout(S),S=setTimeout(()=>{b()},300)});const D=()=>{j.value=null,h.value=!0},A=a=>{j.value=a,h.value=!0},P=()=>{h.value=!1,V.reload({only:["repairOrders"]})},L=()=>{y.value&&(f.value=!0,V.delete(route("repair-orders.destroy",y.value.id),{onSuccess:()=>{O.value=!1,y.value=null,f.value=!1},onError:()=>{f.value=!1}}))},U=()=>{O.value=!1,y.value=null,f.value=!1},b=()=>{const a={search:d.value||void 0,status:g.value!=="all"?g.value:void 0,priority:x.value!=="all"?x.value:void 0,technician:v.value!=="all"?v.value:void 0};V.get(route("repair-orders.index"),a,{preserveState:!0,replace:!0})},F=()=>{d.value="",g.value="all",x.value="all",v.value="all",b()},R=a=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[a]||t.pending},$=a=>{const t={low:"bg-green-100 text-green-800 border-green-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",high:"bg-orange-100 text-orange-800 border-orange-200",urgent:"bg-red-100 text-red-800 border-red-200"};return t[a]||t.medium};return(a,t)=>(l(),n(k,null,[w(c(N),{title:"Repair Orders"}),w(q,null,{header:p(()=>[e("div",W,[t[12]||(t[12]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Repair Orders "),e("p",{class:"text-gray-400 text-sm"},"Track and manage repair service requests")],-1)),e("div",J,[e("div",K,[M(e("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>d.value=r),type:"text",placeholder:"Search orders...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[E,d.value]]),t[7]||(t[7]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),d.value?(l(),n("button",{key:0,onClick:t[1]||(t[1]=r=>d.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):u("",!0)]),e("div",X,[M(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>g.value=r),onChange:b,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[8]||(t[8]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"in_progress"},"In Progress",-1),e("option",{value:"waiting_parts"},"Waiting Parts",-1),e("option",{value:"completed"},"Completed",-1),e("option",{value:"delivered"},"Delivered",-1),e("option",{value:"cancelled"},"Cancelled",-1)]),544),[[H,g.value]]),M(e("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>x.value=r),onChange:b,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[9]||(t[9]=[e("option",{value:"all"},"All Priority",-1),e("option",{value:"low"},"Low",-1),e("option",{value:"medium"},"Medium",-1),e("option",{value:"high"},"High",-1),e("option",{value:"urgent"},"Urgent",-1)]),544),[[H,x.value]]),M(e("select",{"onUpdate:modelValue":t[4]||(t[4]=r=>v.value=r),onChange:b,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[t[10]||(t[10]=e("option",{value:"all"},"All Technicians",-1)),(l(!0),n(k,null,B(s.technicians,r=>(l(),n("option",{key:r.id,value:r.id},o(r.name),9,Y))),128))],544),[[H,v.value]])]),e("div",Z,[d.value||g.value!=="all"||x.value!=="all"||v.value!=="all"?(l(),n("button",{key:0,onClick:F,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):u("",!0),e("button",{onClick:D,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[11]||(t[11]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Create Order",-1)]))])])])]),default:p(()=>[e("div",ee,[e("div",te,[e("div",re,[e("div",se,[e("div",null,[t[13]||(t[13]=e("p",{class:"text-sm text-gray-400"},"Total Orders",-1)),e("p",oe,o(s.repairOrders.total||0),1)]),t[14]||(t[14]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),e("div",le,[e("div",ae,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm text-gray-400"},"In Progress",-1)),e("p",ne,o(Math.floor((s.repairOrders.total||0)*.3)),1)]),t[16]||(t[16]=e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ie,[e("div",de,[e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm text-gray-400"},"Completed",-1)),e("p",ue,o(Math.floor((s.repairOrders.total||0)*.5)),1)]),t[18]||(t[18]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ce,[e("div",pe,[e("div",null,[t[19]||(t[19]=e("p",{class:"text-sm text-gray-400"},"Pending",-1)),e("p",ge,o(Math.floor((s.repairOrders.total||0)*.15)),1)]),t[20]||(t[20]=e("div",{class:"p-2 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",xe,[e("div",ve,[e("div",null,[t[21]||(t[21]=e("p",{class:"text-sm text-gray-400"},"This Month",-1)),e("p",me,o(Math.floor((s.repairOrders.total||0)*.2)),1)]),t[22]||(t[22]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",he,[e("div",ye,[e("table",fe,[t[27]||(t[27]=e("thead",{class:"bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Order #"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Customer"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Device"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Service"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Priority"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Technician"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Total"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",be,[(l(!0),n(k,null,B(s.repairOrders.data,r=>(l(),n("tr",{key:r.id,class:"hover:bg-gray-800"},[e("td",we,o(r.order_number),1),e("td",ke,o(r.customer?.full_name),1),e("td",Ce,[T(o(r.device?.brand)+" "+o(r.device?.model)+" ",1),e("div",_e,o(r.device?.device_type?.name),1)]),e("td",Me,o(r.service?.name),1),e("td",Oe,[e("span",{class:z(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",R(r.status)])},o(r.status.replace("_"," ").toUpperCase()),3)]),e("td",je,[e("span",{class:z(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",$(r.priority)])},o(r.priority.toUpperCase()),3)]),e("td",Ve,o(r.assigned_technician_name||"Unassigned"),1),e("td",Be,"₱"+o(r.total_cost),1),e("td",Te,[e("div",ze,[w(c(m),{href:a.route("repair-orders.show",r.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Details"},{default:p(()=>t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[23]},1032,["href"]),e("button",{onClick:Re=>A(r),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Order"},t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,He),(r.status==="completed"||r.status==="delivered")&&!r.invoice?(l(),C(c(m),{key:0,href:a.route("repair-orders.generate-invoice",r.id),method:"post",as:"button",class:"p-2 text-green-400 hover:text-green-300 hover:bg-green-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Generate Invoice"},{default:p(()=>t[25]||(t[25]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)])),_:2,__:[25]},1032,["href"])):u("",!0),r.invoice?(l(),C(c(m),{key:1,href:a.route("invoices.show",r.invoice.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Invoice"},{default:p(()=>t[26]||(t[26]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)])),_:2,__:[26]},1032,["href"])):u("",!0)])])]))),128))])])]),s.repairOrders.links?(l(),n("div",Se,[e("div",De,[e("div",Ae,[s.repairOrders.prev_page_url?(l(),C(c(m),{key:0,href:s.repairOrders.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:p(()=>t[28]||(t[28]=[T(" Previous ",-1)])),_:1,__:[28]},8,["href"])):u("",!0),s.repairOrders.next_page_url?(l(),C(c(m),{key:1,href:s.repairOrders.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:p(()=>t[29]||(t[29]=[T(" Next ",-1)])),_:1,__:[29]},8,["href"])):u("",!0)]),e("div",Pe,[e("div",null,[e("p",Le," Showing "+o(s.repairOrders.from)+" to "+o(s.repairOrders.to)+" of "+o(s.repairOrders.total)+" results ",1)]),e("div",null,[e("nav",Ue,[(l(!0),n(k,null,B(s.repairOrders.links||[],r=>(l(),n(k,{key:r?.label||"empty"},[r&&r.url?(l(),C(c(m),{key:0,href:r.url,class:z(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":r.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!r.active}]),innerHTML:r.label},null,8,["href","class","innerHTML"])):r?(l(),n("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:r.label},null,8,Fe)):u("",!0)],64))),128))])])])])])):u("",!0)])]),w(G,{show:h.value,"repair-order":j.value,customers:s.customers,devices:s.devices,services:s.services,technicians:s.technicians,parts:s.parts,onClose:t[5]||(t[5]=r=>h.value=!1),onSaved:P},null,8,["show","repair-order","customers","devices","services","technicians","parts"]),w(Q,{show:O.value,processing:f.value,title:"Delete Repair Order",message:`Are you sure you want to delete repair order ${y.value?.order_number}? This action cannot be undone.`,"confirm-text":"Delete Order","cancel-text":"Cancel",type:"danger",onConfirm:L,onCancel:U},null,8,["show","processing","message"])]),_:1})],64))}};export{Qe as default};
