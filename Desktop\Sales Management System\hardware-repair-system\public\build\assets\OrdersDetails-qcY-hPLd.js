import{_}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as x}from"./Chart-DPkq-W3M.js";import{m as p,g as n,o as i,a as l,d as b,h as D,w as g,b as t,t as o,n as m,F as y,y as B,l as O}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const j={class:"flex items-center justify-between"},S={class:"flex items-center space-x-3"},F={class:"py-6"},A={class:"max-w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-6"},V={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},M={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},z={class:"flex items-center justify-between"},T={class:"text-2xl font-bold text-white"},I={class:"mt-4 flex items-center"},R={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},N={class:"flex items-center justify-between"},U={class:"text-2xl font-bold text-white"},$={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},H={class:"flex items-center justify-between"},L={class:"text-2xl font-bold text-white"},P={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},W={class:"flex items-center justify-between"},E={class:"text-2xl font-bold text-white"},G={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},q={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},J={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-xl"},K={class:"overflow-x-auto"},Q={class:"min-w-full divide-y divide-gray-700"},X={class:"bg-gray-800 divide-y divide-gray-700"},Y={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},tt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},et={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},nt={__name:"OrdersDetails",props:{ordersData:{type:Object,default:()=>({})},ordersByStatus:{type:Object,default:()=>({})},ordersTrend:{type:Array,default:()=>[]},averageOrderValue:{type:Number,default:0},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({})}},setup(r){const u=r,f=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),h=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,v=s=>s>=0?"text-green-400":"text-red-400",w=s=>({pending:"text-yellow-400 bg-yellow-400/20",in_progress:"text-blue-400 bg-blue-400/20",waiting_parts:"text-orange-400 bg-orange-400/20",completed:"text-green-400 bg-green-400/20",delivered:"text-green-400 bg-green-400/20",cancelled:"text-red-400 bg-red-400/20"})[s]||"text-gray-400 bg-gray-400/20",k=p(()=>{const s=u.ordersByStatus||{},e=Object.keys(s),d=Object.values(s);return{labels:e.map(a=>a.replace("_"," ").toUpperCase()),datasets:[{label:"Orders by Status",data:d,backgroundColor:["rgba(245, 158, 11, 0.8)","rgba(59, 130, 246, 0.8)","rgba(249, 115, 22, 0.8)","rgba(16, 185, 129, 0.8)","rgba(34, 197, 94, 0.8)","rgba(239, 68, 68, 0.8)"],borderColor:["rgb(245, 158, 11)","rgb(59, 130, 246)","rgb(249, 115, 22)","rgb(16, 185, 129)","rgb(34, 197, 94)","rgb(239, 68, 68)"],borderWidth:2}]}}),C=p(()=>{const s=u.ordersTrend||[];return{labels:s.map(e=>e.date||""),datasets:[{label:"Daily Orders",data:s.map(e=>parseInt(e.count||0)),borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!0}]}}),c={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}};return(s,e)=>(i(),n(y,null,[l(b(D),{title:"Orders Details"}),l(_,null,{header:g(()=>[t("div",j,[t("div",null,[t("div",S,[l(b(O),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:g(()=>e[0]||(e[0]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[0]},8,["href"]),e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Orders Details ",-1))]),e[2]||(e[2]=t("p",{class:"text-sm text-gray-400 mt-1"}," Comprehensive order analytics and insights ",-1))])])]),default:g(()=>[t("div",F,[t("div",A,[t("div",V,[t("div",M,[t("div",z,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Orders",-1)),t("p",T,o(r.ordersData.total_orders||0),1)]),e[4]||(e[4]=t("div",{class:"p-3 bg-blue-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))]),t("div",I,[t("span",{class:m([v(r.ordersData.orders_growth),"text-sm font-medium"])},o(h(r.ordersData.orders_growth||0)),3),e[5]||(e[5]=t("span",{class:"text-gray-400 text-sm ml-2"},"vs previous period",-1))])]),t("div",R,[t("div",N,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-400"},"Avg Order Value",-1)),t("p",U,o(f(r.averageOrderValue)),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-green-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),t("div",$,[t("div",H,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-400"},"Daily Average",-1)),t("p",L,o(Math.round((r.ordersData.total_orders||0)/30)),1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),t("div",P,[t("div",W,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-400"},"Completion Rate",-1)),t("p",E,o(Math.round(((r.ordersByStatus.completed||0)+(r.ordersByStatus.delivered||0))/(r.ordersData.total_orders||1)*100))+"% ",1)]),e[11]||(e[11]=t("div",{class:"p-3 bg-yellow-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),t("div",G,[t("div",Z,[e[12]||(e[12]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Orders by Status")],-1)),l(x,{type:"doughnut",data:k.value,options:c,height:300},null,8,["data"])]),t("div",q,[e[13]||(e[13]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Orders Trend")],-1)),l(x,{type:"line",data:C.value,options:c,height:300},null,8,["data"])])]),t("div",J,[e[15]||(e[15]=t("div",{class:"px-6 py-4 border-b border-gray-700"},[t("h3",{class:"text-lg font-semibold text-white"},"Orders Status Breakdown")],-1)),t("div",K,[t("table",Q,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Status"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Count"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Percentage")])],-1)),t("tbody",X,[(i(!0),n(y,null,B(r.ordersByStatus,(d,a)=>(i(),n("tr",{key:a,class:"hover:bg-gray-700"},[t("td",Y,[t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",w(a)])},o(a.replace("_"," ").toUpperCase()),3)]),t("td",tt,o(d),1),t("td",et,o(Math.round(d/(r.ordersData.total_orders||1)*100))+"% ",1)]))),128))])])])])])])]),_:1})],64))}};export{nt as default};
