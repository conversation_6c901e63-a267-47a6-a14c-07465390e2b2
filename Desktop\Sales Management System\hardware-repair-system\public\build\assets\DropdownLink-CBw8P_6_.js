import{p as y,D as w,m as c,r as h,g as k,o as f,b as o,j as u,a as C,s as l,N as d,w as g,n as p,T as b,c as S,d as _,l as $}from"./app-BGh5SFxi.js";const x={class:"relative"},B={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:String,default:"py-1 bg-white"}},setup(a){const s=a,r=n=>{e.value&&n.key==="Escape"&&(e.value=!1)};y(()=>document.addEventListener("keydown",r)),w(()=>document.removeEventListener("keydown",r));const v=c(()=>({48:"w-48"})[s.width.toString()]),m=c(()=>s.align==="left"?"ltr:origin-top-left rtl:origin-top-right start-0":s.align==="right"?"ltr:origin-top-right rtl:origin-top-left end-0":"origin-top"),e=h(!1);return(n,t)=>(f(),k("div",x,[o("div",{onClick:t[0]||(t[0]=i=>e.value=!e.value)},[l(n.$slots,"trigger")]),u(o("div",{class:"fixed inset-0 z-40",onClick:t[1]||(t[1]=i=>e.value=!1)},null,512),[[d,e.value]]),C(b,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:g(()=>[u(o("div",{class:p(["absolute z-50 mt-2 rounded-md shadow-lg",[v.value,m.value]]),style:{display:"none"},onClick:t[2]||(t[2]=i=>e.value=!1)},[o("div",{class:p(["rounded-md ring-1 ring-black ring-opacity-5",a.contentClasses])},[l(n.$slots,"content")],2)],2),[[d,e.value]])]),_:3})]))}},D={__name:"DropdownLink",props:{href:{type:String,required:!0}},setup(a){return(s,r)=>(f(),S(_($),{href:a.href,class:"block w-full px-4 py-2 text-start text-sm leading-5 text-gray-700 transition duration-150 ease-in-out hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"},{default:g(()=>[l(s.$slots,"default")]),_:3},8,["href"]))}};export{D as _,B as a};
