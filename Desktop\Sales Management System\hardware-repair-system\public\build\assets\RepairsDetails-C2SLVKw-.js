import{_ as v}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as p}from"./Chart-DPkq-W3M.js";import{m as c,g as u,o as g,a as i,d as m,h as w,w as x,b as t,t as r,F as b,y as _,l as k}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const C={class:"flex items-center justify-between"},D={class:"flex items-center space-x-3"},j={class:"py-6"},R={class:"max-w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-6"},B={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},T={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},A={class:"flex items-center justify-between"},F={class:"text-2xl font-bold text-white"},M={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},O={class:"flex items-center justify-between"},P={class:"text-2xl font-bold text-white"},S={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},I={class:"flex items-center justify-between"},U={class:"text-2xl font-bold text-white"},z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},N={class:"flex items-center justify-between"},V={class:"text-2xl font-bold text-white"},W={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},L={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},E={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},$={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-xl"},q={class:"overflow-x-auto"},G={class:"min-w-full divide-y divide-gray-700"},H={class:"bg-gray-800 divide-y divide-gray-700"},J={class:"px-6 py-4 whitespace-nowrap"},K={class:"flex items-center"},Q={class:"flex-shrink-0 h-10 w-10"},X={class:"h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center"},Y={class:"text-sm font-medium text-white"},tt={class:"ml-4"},et={class:"text-sm font-medium text-white"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},ut={__name:"RepairsDetails",props:{repairsData:{type:Object,default:()=>({})},repairsByStatus:{type:Object,default:()=>({})},repairsTrend:{type:Array,default:()=>[]},averageRepairTime:{type:Number,default:0},technicianPerformance:{type:Array,default:()=>[]},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({})}},setup(a){const l=a,y=c(()=>{const s=l.repairsByStatus||{},e=Object.keys(s),o=Object.values(s);return{labels:e.map(n=>n.replace("_"," ").toUpperCase()),datasets:[{label:"Repairs by Status",data:o,backgroundColor:["rgba(245, 158, 11, 0.8)","rgba(59, 130, 246, 0.8)","rgba(249, 115, 22, 0.8)","rgba(16, 185, 129, 0.8)","rgba(34, 197, 94, 0.8)","rgba(239, 68, 68, 0.8)"],borderColor:["rgb(245, 158, 11)","rgb(59, 130, 246)","rgb(249, 115, 22)","rgb(16, 185, 129)","rgb(34, 197, 94)","rgb(239, 68, 68)"],borderWidth:2}]}}),h=c(()=>{const s=l.repairsTrend||[];return{labels:s.map(e=>e.date||""),datasets:[{label:"Daily Repairs",data:s.map(e=>parseInt(e.count||0)),borderColor:"rgb(139, 92, 246)",backgroundColor:"rgba(139, 92, 246, 0.1)",tension:.4,fill:!0}]}}),f=c(()=>{const s=l.technicianPerformance.slice(0,5)||[];return{labels:s.map(e=>e.technician?.user?.name||"Unknown"),datasets:[{label:"Completed Repairs",data:s.map(e=>parseInt(e.completed_orders||0)),backgroundColor:"rgba(16, 185, 129, 0.8)",borderColor:"rgb(16, 185, 129)",borderWidth:2}]}}),d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}};return(s,e)=>(g(),u(b,null,[i(m(w),{title:"Repairs Details"}),i(v,null,{header:x(()=>[t("div",C,[t("div",null,[t("div",D,[i(m(k),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:x(()=>e[0]||(e[0]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[0]},8,["href"]),e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Repairs Details ",-1))]),e[2]||(e[2]=t("p",{class:"text-sm text-gray-400 mt-1"}," Comprehensive repair analytics and insights ",-1))])])]),default:x(()=>[t("div",j,[t("div",R,[t("div",B,[t("div",T,[t("div",A,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Repairs",-1)),t("p",F,r(a.repairsData.total_repairs||0),1)]),e[4]||(e[4]=t("div",{class:"p-3 bg-purple-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"})])],-1))])]),t("div",M,[t("div",O,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-400"},"Active Repairs",-1)),t("p",P,r(a.repairsData.active_repairs||0),1)]),e[6]||(e[6]=t("div",{class:"p-3 bg-blue-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),t("div",S,[t("div",I,[t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-400"},"Completed Repairs",-1)),t("p",U,r(a.repairsData.completed_repairs||0),1)]),e[8]||(e[8]=t("div",{class:"p-3 bg-green-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),t("div",z,[t("div",N,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-400"},"Avg Repair Time",-1)),t("p",V,r(Math.round(a.averageRepairTime))+" days ",1)]),e[10]||(e[10]=t("div",{class:"p-3 bg-yellow-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),t("div",W,[t("div",L,[e[11]||(e[11]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Repairs by Status")],-1)),i(p,{type:"doughnut",data:y.value,options:d,height:300},null,8,["data"])]),t("div",E,[e[12]||(e[12]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Repairs Trend")],-1)),i(p,{type:"line",data:h.value,options:d,height:300},null,8,["data"])])]),t("div",Z,[e[13]||(e[13]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Top Technician Performance")],-1)),i(p,{type:"bar",data:f.value,options:d,height:300},null,8,["data"])]),t("div",$,[e[15]||(e[15]=t("div",{class:"px-6 py-4 border-b border-gray-700"},[t("h3",{class:"text-lg font-semibold text-white"},"Technician Performance")],-1)),t("div",q,[t("table",G,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Technician"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Total Orders"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Completed"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Completion Rate"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg Time")])],-1)),t("tbody",H,[(g(!0),u(b,null,_(a.technicianPerformance,(o,n)=>(g(),u("tr",{key:n,class:"hover:bg-gray-700"},[t("td",J,[t("div",K,[t("div",Q,[t("div",X,[t("span",Y,r((o.technician?.user?.name||"U").charAt(0).toUpperCase()),1)])]),t("div",tt,[t("div",et,r(o.technician?.user?.name||"Unknown"),1)])])]),t("td",st,r(o.total_orders),1),t("td",rt,r(o.completed_orders),1),t("td",ot,r(Math.round(o.completed_orders/(o.total_orders||1)*100))+"% ",1),t("td",at,r(Math.round(o.avg_completion_time))+" days ",1)]))),128))])])])])])])]),_:1})],64))}};export{ut as default};
