import{r as g,x as Q,q as S,g as i,o as a,a as V,d as h,h as R,w,b as e,i as d,t as n,j as k,v as T,I as J,k as I,n as _,F as B,y as $,l as j,f as D,c as F}from"./app-BGh5SFxi.js";import{_ as K}from"./AuthenticatedLayout-D7wPPUcl.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const W={class:"py-6"},X={class:"max-w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-bold text-white"},oe={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},re={class:"flex items-center"},le={class:"ml-4"},ne={class:"text-2xl font-bold text-white"},ae={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},ie={class:"flex items-center"},de={class:"ml-4"},ue={class:"text-2xl font-bold text-white"},ce={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},ge={class:"flex items-center"},ve={class:"ml-4"},xe={class:"text-2xl font-bold text-white"},pe={class:"bg-gray-800 rounded-xl shadow-2xl border border-gray-700 p-4"},me={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4"},fe={class:"md:col-span-2"},ye={key:0,class:"bg-blue-900/20 border border-blue-700 rounded-lg p-4 mb-6"},be={class:"flex items-center justify-between"},he={class:"flex items-center space-x-4"},we={class:"text-blue-400 font-medium"},ke={class:"bg-gray-800 rounded-xl shadow-2xl border border-gray-700 overflow-hidden"},_e={class:"overflow-x-auto"},Ce={class:"min-w-full divide-y divide-gray-700"},Me={class:"bg-gray-900"},je={class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-12"},Se={class:"flex items-center space-x-1"},Ve={class:"flex items-center space-x-1"},Be={class:"flex items-center space-x-1"},Pe={class:"bg-gray-800 divide-y divide-gray-700"},Te={class:"px-4 py-3 whitespace-nowrap"},De=["value","onChange"],Fe={class:"px-4 py-3 whitespace-nowrap"},ze={class:"text-sm font-medium text-white"},Ae={class:"px-4 py-3 whitespace-nowrap"},Ie={class:"flex items-center justify-between"},$e={class:"text-sm text-white"},Ue={class:"text-xs text-gray-400"},Le={class:"px-4 py-3 whitespace-nowrap"},He={class:"text-sm text-gray-300"},Ne={class:"px-4 py-3 whitespace-nowrap"},Oe={class:"text-sm text-gray-300"},qe={class:"px-4 py-3 whitespace-nowrap"},Ee={class:"text-sm font-medium text-white"},Ge={key:0,class:"text-xs text-green-400"},Qe={class:"px-4 py-3 whitespace-nowrap"},Re={class:"px-4 py-3 whitespace-nowrap text-right text-sm font-medium"},Je={class:"flex items-center space-x-2"},Ke=["onClick"],We={key:0,class:"bg-gray-900 px-4 py-3 border-t border-gray-700 sm:px-6"},Xe={class:"flex items-center justify-between"},Ye={class:"flex-1 flex justify-between sm:hidden"},Ze={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},et={class:"text-sm text-gray-400"},tt={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},st=["innerHTML"],at={__name:"Index",props:{invoices:Object,stats:Object,filters:Object},setup(l){const v=l,m=g(v.filters?.search||""),f=g(v.filters?.status||"all"),C=g(v.filters?.date_from||""),M=g(v.filters?.date_to||""),x=g(v.filters?.sort||"created_at"),u=g(v.filters?.direction||"desc"),r=g([]),p=g(!1),y=g(!1);let z;Q(m,o=>{clearTimeout(z),z=setTimeout(()=>{b()},300)});const b=()=>{const o={search:m.value||void 0,status:f.value!=="all"?f.value:void 0,date_from:C.value||void 0,date_to:M.value||void 0,sort:x.value!=="created_at"?x.value:void 0,direction:u.value!=="desc"?u.value:void 0};S.get(route("invoices.index"),o,{preserveState:!0,replace:!0})},U=()=>{m.value="",f.value="all",C.value="",M.value="",x.value="created_at",u.value="desc",b()},P=o=>{x.value===o?u.value=u.value==="asc"?"desc":"asc":(x.value=o,u.value="asc"),b()},L=o=>({pending:"text-yellow-400 bg-yellow-900/50 border-yellow-700",paid:"text-green-400 bg-green-900/50 border-green-700",partially_paid:"text-orange-400 bg-orange-900/50 border-orange-700",cancelled:"text-gray-400 bg-gray-900/50 border-gray-700"})[o]||"text-gray-400 bg-gray-900/50 border-gray-700",A=o=>"₱"+parseFloat(o).toLocaleString("en-US",{minimumFractionDigits:2}),H=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),N=o=>{confirm(`Are you sure you want to delete invoice ${o.invoice_number}?`)&&S.delete(route("invoices.destroy",o.id),{onSuccess:()=>{window.toast&&window.toast.success("Invoice deleted successfully!")}})},O=()=>{p.value?r.value=v.invoices.data.map(o=>o.id):r.value=[],y.value=r.value.length>0},q=o=>{const t=r.value.indexOf(o);t>-1?r.value.splice(t,1):r.value.push(o),p.value=r.value.length===v.invoices.data.length,y.value=r.value.length>0},E=()=>{confirm(`Mark ${r.value.length} invoices as paid?`)&&S.post(route("invoices.bulk-mark-paid"),{invoice_ids:r.value},{onSuccess:()=>{r.value=[],p.value=!1,y.value=!1}})},G=()=>{confirm(`Delete ${r.value.length} invoices? This action cannot be undone.`)&&S.delete(route("invoices.bulk-delete"),{data:{invoice_ids:r.value},onSuccess:()=>{r.value=[],p.value=!1,y.value=!1}})};return(o,t)=>(a(),i(B,null,[V(h(R),{title:"Invoices"}),V(K,null,{header:w(()=>t[10]||(t[10]=[e("div",{class:"flex items-center justify-between"},[e("h2",{class:"text-xl font-semibold leading-tight text-white"}," Invoices "),e("div",{class:"text-sm text-gray-400"}," Generate invoices from completed repair orders ")],-1)])),default:w(()=>[e("div",W,[e("div",X,[e("div",Y,[e("div",Z,[e("div",ee,[t[12]||(t[12]=e("div",{class:"p-2 bg-blue-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",te,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Invoices",-1)),e("p",se,n(l.stats.total),1)])])]),e("div",oe,[e("div",re,[t[14]||(t[14]=e("div",{class:"p-2 bg-yellow-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",le,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-gray-400"},"Pending",-1)),e("p",ne,n(l.stats.pending),1)])])]),e("div",ae,[e("div",ie,[t[16]||(t[16]=e("div",{class:"p-2 bg-green-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",de,[t[15]||(t[15]=e("p",{class:"text-sm font-medium text-gray-400"},"Paid",-1)),e("p",ue,n(l.stats.paid),1)])])]),e("div",ce,[e("div",ge,[t[18]||(t[18]=e("div",{class:"p-2 bg-orange-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",ve,[t[17]||(t[17]=e("p",{class:"text-sm font-medium text-gray-400"},"Partially Paid",-1)),e("p",xe,n(l.stats.partially_paid),1)])])])]),e("div",pe,[e("div",me,[e("div",fe,[k(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>m.value=s),type:"text",placeholder:"Search invoices...",class:"w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,512),[[T,m.value]])]),e("div",null,[k(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>f.value=s),onChange:b,class:"w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[19]||(t[19]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"paid"},"Paid",-1),e("option",{value:"partially_paid"},"Partially Paid",-1),e("option",{value:"cancelled"},"Cancelled",-1)]),544),[[J,f.value]])]),e("div",null,[k(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>C.value=s),onChange:b,type:"date",class:"w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,544),[[T,C.value]])]),e("div",null,[k(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>M.value=s),onChange:b,type:"date",class:"w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,544),[[T,M.value]])]),e("div",null,[m.value||f.value!=="all"||C.value||M.value?(a(),i("button",{key:0,onClick:U,class:"w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear Filters ")):d("",!0)])])]),y.value?(a(),i("div",ye,[e("div",be,[e("div",he,[e("span",we,n(r.value.length)+" invoice(s) selected",1),e("button",{onClick:t[4]||(t[4]=s=>{r.value=[],p.value=!1,y.value=!1}),class:"text-gray-400 hover:text-white text-sm"}," Clear selection ")]),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:E,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200"}," Mark as Paid "),e("button",{onClick:G,class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200"}," Delete Selected ")])])])):d("",!0),e("div",ke,[e("div",_e,[e("table",Ce,[e("thead",Me,[e("tr",null,[e("th",je,[k(e("input",{type:"checkbox","onUpdate:modelValue":t[5]||(t[5]=s=>p.value=s),onChange:O,class:"rounded border-gray-600 bg-gray-700 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800"},null,544),[[I,p.value]])]),e("th",{onClick:t[6]||(t[6]=s=>P("invoice_number")),class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-800 transition-colors duration-200 w-32"},[e("div",Se,[t[21]||(t[21]=e("span",null,"Invoice #",-1)),x.value==="invoice_number"?(a(),i("svg",{key:0,class:_(["w-4 h-4",u.value==="asc"?"transform rotate-180":""]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[20]||(t[20]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):d("",!0)])]),t[26]||(t[26]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Customer",-1)),t[27]||(t[27]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-32"},"Repair Order",-1)),e("th",{onClick:t[7]||(t[7]=s=>P("issue_date")),class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-800 transition-colors duration-200 w-28"},[e("div",Ve,[t[23]||(t[23]=e("span",null,"Issue Date",-1)),x.value==="issue_date"?(a(),i("svg",{key:0,class:_(["w-4 h-4",u.value==="asc"?"transform rotate-180":""]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[22]||(t[22]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):d("",!0)])]),e("th",{onClick:t[8]||(t[8]=s=>P("total_amount")),class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-800 transition-colors duration-200 w-24"},[e("div",Be,[t[25]||(t[25]=e("span",null,"Amount",-1)),x.value==="total_amount"?(a(),i("svg",{key:0,class:_(["w-4 h-4",u.value==="asc"?"transform rotate-180":""]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[24]||(t[24]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):d("",!0)])]),t[28]||(t[28]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-24"},"Status",-1)),t[29]||(t[29]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-32"},"Actions",-1))])]),e("tbody",Pe,[(a(!0),i(B,null,$(l.invoices.data,s=>(a(),i("tr",{key:s.id,class:"hover:bg-gray-750 transition-colors duration-200"},[e("td",Te,[k(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[9]||(t[9]=c=>r.value=c),onChange:c=>q(s.id),class:"rounded border-gray-600 bg-gray-700 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800"},null,40,De),[[I,r.value]])]),e("td",Fe,[e("div",ze,n(s.invoice_number),1)]),e("td",Ae,[e("div",Ie,[e("div",null,[e("div",$e,n(s.customer.first_name)+" "+n(s.customer.last_name),1),e("div",Ue,n(s.customer.email),1)]),V(h(j),{href:o.route("customers.show",s.customer.id),class:"ml-2 inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors duration-200"},{default:w(()=>t[30]||(t[30]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1),D(" View ",-1)])),_:2,__:[30]},1032,["href"])])]),e("td",Le,[e("div",He,n(s.repair_order.order_number),1)]),e("td",Ne,[e("div",Oe,n(H(s.issue_date)),1)]),e("td",qe,[e("div",Ee,n(A(s.total_amount)),1),s.amount_paid>0?(a(),i("div",Ge," Paid: "+n(A(s.amount_paid)),1)):d("",!0)]),e("td",Qe,[e("span",{class:_(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",L(s.status)])},n(s.status.replace("_"," ").charAt(0).toUpperCase()+s.status.replace("_"," ").slice(1)),3)]),e("td",Re,[e("div",Je,[V(h(j),{href:o.route("invoices.show",s.id),class:"text-blue-400 hover:text-blue-300 transition-colors duration-200",title:"View"},{default:w(()=>t[31]||(t[31]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[31]},1032,["href"]),e("button",{onClick:c=>N(s),class:"text-red-400 hover:text-red-300 transition-colors duration-200",title:"Delete"},t[32]||(t[32]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ke)])])]))),128))])])]),l.invoices.links?(a(),i("div",We,[e("div",Xe,[e("div",Ye,[l.invoices.prev_page_url?(a(),F(h(j),{key:0,href:l.invoices.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:w(()=>t[33]||(t[33]=[D(" Previous ",-1)])),_:1,__:[33]},8,["href"])):d("",!0),l.invoices.next_page_url?(a(),F(h(j),{key:1,href:l.invoices.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:w(()=>t[34]||(t[34]=[D(" Next ",-1)])),_:1,__:[34]},8,["href"])):d("",!0)]),e("div",Ze,[e("div",null,[e("p",et," Showing "+n(l.invoices.from)+" to "+n(l.invoices.to)+" of "+n(l.invoices.total)+" results ",1)]),e("div",null,[e("nav",tt,[(a(!0),i(B,null,$(l.invoices.links||[],(s,c)=>(a(),i(B,{key:c},[s&&s.url?(a(),F(h(j),{key:0,href:s.url,innerHTML:s.label,class:_(["relative inline-flex items-center px-2 py-2 border text-sm font-medium transition-colors duration-200",[s.active?"z-10 bg-red-600 border-red-600 text-white":"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700",c===0?"rounded-l-md":"",c===(l.invoices.links||[]).length-1?"rounded-r-md":""]])},null,8,["href","innerHTML","class"])):s?(a(),i("span",{key:1,innerHTML:s.label,class:_(["relative inline-flex items-center px-2 py-2 border border-gray-600 bg-gray-800 text-gray-500 text-sm font-medium cursor-not-allowed",[c===0?"rounded-l-md":"",c===l.invoices.links.length-1?"rounded-r-md":""]])},null,10,st)):d("",!0)],64))),128))])])])])])):d("",!0)])])])]),_:1})],64))}};export{at as default};
