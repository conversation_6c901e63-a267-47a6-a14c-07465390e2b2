import{r as j,m as p,g as i,o as n,a as d,d as f,h as L,w as x,b as t,t as a,i as P,F as g,y as h,n as v,l as B,j as V,I as D}from"./app-BGh5SFxi.js";import{_ as M}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as y}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const U={class:"flex justify-between items-center"},N={class:"flex items-center space-x-4"},A={class:"flex items-center space-x-4"},F=["value"],I={class:"py-12"},O={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},z={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},q={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},T={class:"flex items-center"},E={class:"ml-4"},H={class:"text-2xl font-semibold text-white"},R={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},Y={class:"flex items-center"},$={class:"ml-4"},G={class:"text-2xl font-semibold text-white"},J={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},K={class:"flex items-center"},Q={class:"ml-4"},W={class:"text-2xl font-semibold text-white"},X={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-white"},st={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ot={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},at={class:"p-6"},lt={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},rt={class:"p-6"},it={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},nt={class:"p-6"},dt={class:"overflow-x-auto"},ct={class:"min-w-full divide-y divide-gray-700"},ut={class:"bg-gray-800 divide-y divide-gray-700"},mt={class:"px-6 py-4 whitespace-nowrap"},xt={class:"text-sm font-medium text-white"},gt={class:"px-6 py-4 whitespace-nowrap"},pt={class:"text-sm text-gray-300"},ft={class:"px-6 py-4 whitespace-nowrap"},ht={class:"px-6 py-4 whitespace-nowrap"},vt={class:"text-sm text-gray-300"},yt={class:"px-6 py-4 whitespace-nowrap"},wt={key:0},Lt={__name:"PartsAnalytics",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(l){const r=l,c=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),u=s=>parseFloat(s||0).toLocaleString("en-US"),w=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],m=j(r.period),_=()=>{window.location.href=route("reports.parts-analytics",{period:m.value})},b=p(()=>({type:"line",data:{labels:r.charts.parts_usage_trend?.map(s=>new Date(s.date).toLocaleDateString())||[],datasets:[{label:"Parts Usage",data:r.charts.parts_usage_trend?.map(s=>s.quantity)||[],borderColor:"rgb(245, 158, 11)",backgroundColor:"rgba(245, 158, 11, 0.1)",tension:.4,fill:!0}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),k=p(()=>({type:"doughnut",data:{labels:r.charts.parts_by_category?.map(s=>s.category||"Unknown")||[],datasets:[{data:r.charts.parts_by_category?.map(s=>s.value)||[],backgroundColor:["#f59e0b","#10b981","#3b82f6","#ef4444","#8b5cf6","#06b6d4","#84cc16","#f97316"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}})),C=(s,e=10)=>s===0?"text-red-400":s<=e?"text-yellow-400":"text-green-400",S=(s,e=10)=>s===0?"Out of Stock":s<=e?"Low Stock":"In Stock";return(s,e)=>(n(),i(g,null,[d(f(L),{title:"Parts Analytics"}),d(M,null,{header:x(()=>[t("div",U,[t("div",N,[d(f(B),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:x(()=>e[1]||(e[1]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),e[2]||(e[2]=t("h2",{class:"font-semibold text-xl text-white leading-tight"}," Parts Analytics ",-1))]),t("div",A,[V(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>m.value=o),onChange:_,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(n(),i(g,null,h(w,o=>t("option",{key:o.value,value:o.value},a(o.label),9,F)),64))],544),[[D,m.value]])])])]),default:x(()=>[t("div",I,[t("div",O,[t("div",z,[t("div",q,[t("div",T,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])])],-1)),t("div",E,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"Inventory Value",-1)),t("p",H,a(c(l.metrics.total_inventory_value)),1)])])]),t("div",R,[t("div",Y,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),t("div",$,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-400"},"Low Stock Items",-1)),t("p",G,a(u(l.metrics.low_stock_count)),1)])])]),t("div",J,[t("div",K,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Q,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-400"},"Parts Used",-1)),t("p",W,a(u(l.metrics.total_parts_used)),1)])])]),t("div",X,[t("div",Z,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",tt,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-400"},"Avg. Part Cost",-1)),t("p",et,a(c(l.metrics.average_part_cost)),1)])])])]),t("div",st,[t("div",ot,[t("div",at,[e[11]||(e[11]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Parts Usage Trend",-1)),d(y,{config:b.value},null,8,["config"])])]),t("div",lt,[t("div",rt,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Inventory Value by Category",-1)),d(y,{config:k.value},null,8,["config"])])])]),t("div",it,[t("div",nt,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-white mb-6"},"Low Stock Parts",-1)),t("div",dt,[t("table",ct,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Part Name "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Category "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Current Stock "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Cost Price "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Status ")])],-1)),t("tbody",ut,[(n(!0),i(g,null,h(l.metrics.low_stock_parts,o=>(n(),i("tr",{key:o.id,class:"hover:bg-gray-700"},[t("td",mt,[t("div",xt,a(o.name),1)]),t("td",gt,[t("div",pt,a(o.category||"N/A"),1)]),t("td",ft,[t("div",{class:v(["text-sm font-medium",C(o.quantity_in_stock)])},a(u(o.quantity_in_stock)),3)]),t("td",ht,[t("div",vt,a(c(o.cost_price)),1)]),t("td",yt,[t("span",{class:v(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",o.quantity_in_stock===0?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"])},a(S(o.quantity_in_stock)),3)])]))),128)),!l.metrics.low_stock_parts||l.metrics.low_stock_parts.length===0?(n(),i("tr",wt,e[13]||(e[13]=[t("td",{colspan:"5",class:"px-6 py-4 text-center text-gray-400"}," No low stock parts found. ",-1)]))):P("",!0)])])])])])])])]),_:1})],64))}};export{Lt as default};
