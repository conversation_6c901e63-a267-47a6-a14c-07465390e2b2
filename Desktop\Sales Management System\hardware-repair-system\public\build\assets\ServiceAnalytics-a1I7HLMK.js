import{r as P,m as f,g as a,o as d,a as n,d as h,h as j,w as m,b as e,t as i,i as M,F as u,y as v,n as A,l as B,j as L,I as N}from"./app-BGh5SFxi.js";import{_ as D}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as y}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const F={class:"flex justify-between items-center"},V={class:"flex items-center space-x-4"},T={class:"flex items-center space-x-4"},O=["value"],U={class:"py-12"},z={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},E={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},R={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},$={class:"flex items-center"},G={class:"ml-4"},H={class:"text-2xl font-semibold text-white"},I={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},W={class:"flex items-center"},Y={class:"ml-4"},q={class:"text-lg font-semibold text-white truncate"},J={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},K={class:"flex items-center"},Q={class:"ml-4"},X={class:"text-2xl font-semibold text-white"},Z={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-semibold text-white"},oe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ie={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},re={class:"p-6"},le={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ae={class:"p-6"},de={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ne={class:"p-6"},ce={class:"overflow-x-auto"},pe={class:"min-w-full divide-y divide-gray-700"},me={class:"bg-gray-800 divide-y divide-gray-700"},ue={class:"px-6 py-4 whitespace-nowrap"},xe={class:"text-sm font-medium text-white"},ge={class:"text-sm text-gray-400"},fe={class:"px-6 py-4 whitespace-nowrap"},he={class:"text-sm font-medium text-blue-400"},ve={class:"px-6 py-4 whitespace-nowrap"},ye={class:"text-sm text-gray-300"},_e={class:"px-6 py-4 whitespace-nowrap"},we={class:"text-sm text-gray-300"},be={class:"px-6 py-4 whitespace-nowrap"},ke={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"text-sm text-gray-300"},Se={key:0},Ne={__name:"ServiceAnalytics",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(r){const l=r,x=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),c=s=>parseFloat(s||0).toLocaleString("en-US"),g=s=>`${parseFloat(s||0).toFixed(1)}%`,_=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],p=P(l.period),w=()=>{window.location.href=route("reports.service-analytics",{period:p.value})},b=f(()=>({type:"bar",data:{labels:l.charts.service_popularity?.map(s=>s.service_name)||[],datasets:[{label:"Service Count",data:l.charts.service_popularity?.map(s=>s.count)||[],backgroundColor:"rgba(16, 185, 129, 0.8)",borderColor:"rgb(16, 185, 129)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),k=f(()=>({type:"doughnut",data:{labels:l.charts.service_profitability?.map(s=>s.service_name)||[],datasets:[{data:l.charts.service_profitability?.map(s=>s.profit_margin)||[],backgroundColor:["#10b981","#3b82f6","#f59e0b","#ef4444","#8b5cf6","#06b6d4","#84cc16","#f97316"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}})),C=s=>s>=50?"bg-green-100 text-green-800":s>=30?"bg-blue-100 text-blue-800":s>=15?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",S=s=>s>=50?"Excellent":s>=30?"Good":s>=15?"Average":"Poor";return(s,t)=>(d(),a(u,null,[n(h(j),{title:"Service Analytics"}),n(D,null,{header:m(()=>[e("div",F,[e("div",V,[n(h(B),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:m(()=>t[1]||(t[1]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),t[2]||(t[2]=e("h2",{class:"font-semibold text-xl text-white leading-tight"}," Service Analytics ",-1))]),e("div",T,[L(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>p.value=o),onChange:w,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(d(),a(u,null,v(_,o=>e("option",{key:o.value,value:o.value},i(o.label),9,O)),64))],544),[[N,p.value]])])])]),default:m(()=>[e("div",U,[e("div",z,[e("div",E,[e("div",R,[e("div",$,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])])],-1)),e("div",G,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Services",-1)),e("p",H,i(c(r.metrics.total_services)),1)])])]),e("div",I,[e("div",W,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),e("div",Y,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-400"},"Most Popular",-1)),e("p",q,i(r.metrics.most_popular_service||"N/A"),1)])])]),e("div",J,[e("div",K,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Q,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-400"},"Avg. Service Time",-1)),e("p",X,i(c(r.metrics.average_service_time))+" hrs",1)])])]),e("div",Z,[e("div",ee,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),e("div",te,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-400"},"Avg. Profit Margin",-1)),e("p",se,i(g(r.metrics.average_profit_margin)),1)])])])]),e("div",oe,[e("div",ie,[e("div",re,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Most Popular Services",-1)),n(y,{config:b.value},null,8,["config"])])]),e("div",le,[e("div",ae,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Service Profitability",-1)),n(y,{config:k.value},null,8,["config"])])])]),e("div",de,[e("div",ne,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-white mb-6"},"Service Performance Details",-1)),e("div",ce,[e("table",pe,[t[14]||(t[14]=e("thead",{class:"bg-gray-700"},[e("tr",null,[e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Service Name "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Times Performed "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Completion Time "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Total Revenue "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Profit Margin "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Price ")])],-1)),e("tbody",me,[(d(!0),a(u,null,v(r.metrics.service_details,o=>(d(),a("tr",{key:o.id,class:"hover:bg-gray-700"},[e("td",ue,[e("div",xe,i(o.name),1),e("div",ge,i(o.description||"No description"),1)]),e("td",fe,[e("div",he,i(c(o.times_performed)),1)]),e("td",ve,[e("div",ye,i(c(o.avg_completion_time))+" hrs",1)]),e("td",_e,[e("div",we,i(x(o.total_revenue)),1)]),e("td",be,[e("span",{class:A(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",C(o.profit_margin)])},i(S(o.profit_margin))+" ("+i(g(o.profit_margin))+") ",3)]),e("td",ke,[e("div",Ce,i(x(o.avg_price)),1)])]))),128)),!r.metrics.service_details||r.metrics.service_details.length===0?(d(),a("tr",Se,t[13]||(t[13]=[e("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-400"}," No service data available for the selected period. ",-1)]))):M("",!0)])])])])])])])]),_:1})],64))}};export{Ne as default};
