import{r as j,m as g,g as f,o as h,a as r,d as a,h as M,w as i,b as e,t as l,l as d,j as C,F as x,y as O,I as S}from"./app-BGh5SFxi.js";import{_ as z}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as p}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const R={class:"flex justify-between items-center"},B={class:"flex items-center space-x-4"},D=["value"],L={class:"py-12"},A={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},P={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},V={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},F={class:"flex items-center"},H={class:"ml-4"},T={class:"text-2xl font-semibold text-white"},I={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},N={class:"flex items-center"},U={class:"ml-4"},$={class:"text-2xl font-semibold text-white"},E={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},Y={class:"flex items-center"},q={class:"ml-4"},G={class:"text-2xl font-semibold text-white"},J={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},K={class:"flex items-center"},Q={class:"ml-4"},W={class:"text-2xl font-semibold text-white"},X={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Z={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ee={class:"p-6"},te={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},se={class:"p-6"},oe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},le={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},re={class:"space-y-3"},ie={class:"flex justify-between"},ae={class:"text-white font-medium"},ne={class:"flex justify-between"},de={class:"text-yellow-400 font-medium"},ce={class:"flex justify-between"},ue={class:"text-white font-medium"},ve={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},me={class:"space-y-3"},ge={class:"flex justify-between"},fe={class:"text-white font-medium"},he={class:"flex justify-between"},xe={class:"text-red-400 font-medium"},pe={class:"flex justify-between"},we={class:"text-white font-medium"},ye={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},be={class:"space-y-3"},ke={class:"flex justify-between"},_e={class:"text-blue-400 font-medium"},je={class:"flex justify-between"},Me={class:"text-green-400 font-medium"},Ce={class:"flex justify-between"},Oe={class:"text-white font-medium"},Se={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Pe={__name:"Dashboard",props:{overview:{type:Object,default:()=>({})},customerMetrics:{type:Object,default:()=>({})},salesMetrics:{type:Object,default:()=>({})},repairMetrics:{type:Object,default:()=>({})},partsMetrics:{type:Object,default:()=>({})},technicianMetrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(o){const c=o,n=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),u=s=>parseFloat(s||0).toLocaleString("en-US"),w=s=>`${parseFloat(s||0).toFixed(1)}%`,y=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],m=j(c.period),b=()=>{window.location.href=route("reports.index",{period:m.value})},k=g(()=>({type:"line",data:{labels:c.charts.revenue_trend?.map(s=>new Date(s.date).toLocaleDateString())||[],datasets:[{label:"Revenue",data:c.charts.revenue_trend?.map(s=>s.revenue)||[],borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.1)",tension:.4,fill:!0}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),_=g(()=>({type:"doughnut",data:{labels:Object.keys(c.charts.repairs_by_status||{}),datasets:[{data:Object.values(c.charts.repairs_by_status||{}),backgroundColor:["#f59e0b","#3b82f6","#ef4444","#10b981","#6b7280","#22c55e"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}}));return(s,t)=>(h(),f(x,null,[r(a(M),{title:"Reports Dashboard"}),r(z,null,{header:i(()=>[e("div",R,[t[1]||(t[1]=e("h2",{class:"font-semibold text-xl text-white leading-tight"}," Reports Dashboard ",-1)),e("div",B,[C(e("select",{"onUpdate:modelValue":t[0]||(t[0]=v=>m.value=v),onChange:b,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(h(),f(x,null,O(y,v=>e("option",{key:v.value,value:v.value},l(v.label),9,D)),64))],544),[[S,m.value]])])])]),default:i(()=>[e("div",L,[e("div",A,[e("div",P,[e("div",V,[e("div",F,[t[3]||(t[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),e("div",H,[t[2]||(t[2]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Revenue",-1)),e("p",T,l(n(o.overview.total_revenue)),1)])])]),e("div",I,[e("div",N,[t[5]||(t[5]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])])],-1)),e("div",U,[t[4]||(t[4]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Orders",-1)),e("p",$,l(u(o.overview.total_orders)),1)])])]),e("div",E,[e("div",Y,[t[7]||(t[7]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),e("div",q,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-400"},"New Customers",-1)),e("p",G,l(u(o.customerMetrics.new_customers)),1)])])]),e("div",J,[e("div",K,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Q,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-400"},"Completion Rate",-1)),e("p",W,l(w(o.overview.completion_rate)),1)])])])]),e("div",X,[e("div",Z,[e("div",ee,[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Revenue Trend",-1)),r(p,{config:k.value},null,8,["config"])])]),e("div",te,[e("div",se,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Repairs by Status",-1)),r(p,{config:_.value},null,8,["config"])])])]),e("div",oe,[e("div",le,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Sales Overview",-1)),e("div",re,[e("div",ie,[t[12]||(t[12]=e("span",{class:"text-gray-400"},"Total Sales:",-1)),e("span",ae,l(n(o.salesMetrics.total_sales)),1)]),e("div",ne,[t[13]||(t[13]=e("span",{class:"text-gray-400"},"Pending Amount:",-1)),e("span",de,l(n(o.salesMetrics.pending_amount)),1)]),e("div",ce,[t[14]||(t[14]=e("span",{class:"text-gray-400"},"Average Order:",-1)),e("span",ue,l(n(o.overview.average_order_value)),1)])])]),e("div",ve,[t[19]||(t[19]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Parts Overview",-1)),e("div",me,[e("div",ge,[t[16]||(t[16]=e("span",{class:"text-gray-400"},"Inventory Value:",-1)),e("span",fe,l(n(o.partsMetrics.total_inventory_value)),1)]),e("div",he,[t[17]||(t[17]=e("span",{class:"text-gray-400"},"Low Stock Items:",-1)),e("span",xe,l(u(o.partsMetrics.low_stock_count)),1)]),e("div",pe,[t[18]||(t[18]=e("span",{class:"text-gray-400"},"Parts Cost:",-1)),e("span",we,l(n(o.overview.total_parts_cost)),1)])])]),e("div",ye,[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Repair Overview",-1)),e("div",be,[e("div",ke,[t[20]||(t[20]=e("span",{class:"text-gray-400"},"Active Repairs:",-1)),e("span",_e,l(u(o.overview.active_repairs)),1)]),e("div",je,[t[21]||(t[21]=e("span",{class:"text-gray-400"},"Completed:",-1)),e("span",Me,l(u(o.overview.completed_orders)),1)]),e("div",Ce,[t[22]||(t[22]=e("span",{class:"text-gray-400"},"Avg. Repair Time:",-1)),e("span",Oe,l(Math.round(o.repairMetrics.average_repair_time||0))+"h",1)])])])]),e("div",Se,[r(a(d),{href:s.route("reports.customer-analytics"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[24]||(t[24]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Customer Analytics"),e("p",{class:"text-gray-400 text-sm"},"Customer growth, retention, and behavior analysis")])],-1)])),_:1,__:[24]},8,["href"]),r(a(d),{href:s.route("reports.sales-analytics"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[25]||(t[25]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Sales Analytics"),e("p",{class:"text-gray-400 text-sm"},"Revenue trends, payment methods, and sales performance")])],-1)])),_:1,__:[25]},8,["href"]),r(a(d),{href:s.route("reports.parts-analytics"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[26]||(t[26]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Parts Analytics"),e("p",{class:"text-gray-400 text-sm"},"Inventory management, usage patterns, and cost analysis")])],-1)])),_:1,__:[26]},8,["href"]),r(a(d),{href:s.route("reports.device-repair-analytics"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[27]||(t[27]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Device Repair Analytics"),e("p",{class:"text-gray-400 text-sm"},"Repair trends, device types, and completion analysis")])],-1)])),_:1,__:[27]},8,["href"]),r(a(d),{href:s.route("reports.technician-performance"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[28]||(t[28]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Technician Performance"),e("p",{class:"text-gray-400 text-sm"},"Productivity metrics, workload, and specialization analysis")])],-1)])),_:1,__:[28]},8,["href"]),r(a(d),{href:s.route("reports.services"),class:"bg-gray-800 hover:bg-gray-700 overflow-hidden shadow-xl sm:rounded-lg p-6 transition-colors duration-200 block"},{default:i(()=>t[29]||(t[29]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])])]),e("div",{class:"ml-4"},[e("h3",{class:"text-lg font-medium text-white"},"Service Analytics"),e("p",{class:"text-gray-400 text-sm"},"Popular services, profitability, and completion times")])],-1)])),_:1,__:[29]},8,["href"])])])])]),_:1})],64))}};export{Pe as default};
