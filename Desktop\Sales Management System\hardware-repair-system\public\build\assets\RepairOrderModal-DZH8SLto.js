import{u as X,r as S,m as U,x as V,g as d,i as p,o as l,b as t,e as R,t as n,j as m,I as f,d as o,F as y,y as b,v as x,n as B,f as h,K as Z}from"./app-BGh5SFxi.js";const ee={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},te={class:"flex items-center justify-between mb-6"},se={class:"flex items-center space-x-3"},re={class:"text-xl font-bold text-white"},ie={class:"text-sm text-gray-400"},oe={class:"p-6"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},le=["value"],ne={key:0,class:"mt-1 text-sm text-red-400"},ae=["disabled"],ce={value:"",class:"bg-gray-800 text-white"},ue=["value"],pe={key:0,class:"mt-1 text-sm text-red-400"},me={class:"mb-6"},ge={class:"flex items-center justify-between mb-3"},ve={class:"text-white font-medium"},_e=["onClick"],ye={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},be=["onUpdate:modelValue","onChange"],fe=["value"],xe={key:0,class:"mt-2 text-sm text-red-400"},he=["onUpdate:modelValue","onInput"],we={key:0,class:"mt-2 text-sm text-red-400"},ke={class:"mt-4"},Ce=["onUpdate:modelValue"],Pe=["value"],Se={key:0,class:"mt-1 text-sm text-red-400"},Ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},qe={class:"mt-2"},Oe={key:0,class:"mt-1 text-sm text-red-400"},Ue={key:0,class:"mt-1 text-sm text-red-400"},Ae={key:0,class:"mb-6"},je={class:"flex items-center justify-between mb-3"},Fe={key:0,class:"text-center py-4"},Me={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-3"},Re={class:"flex items-center justify-between mb-2"},Be={class:"flex items-center"},$e={class:"text-sm font-medium text-white"},ze={key:0,class:"ml-2 inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-900 text-green-300 border border-green-600"},De={key:1,class:"ml-2 inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-900 text-blue-300 border border-blue-600"},Ne=["onClick"],Te={class:"text-xs text-gray-400 mb-1"},Ee={class:"flex items-center justify-between text-xs"},He={class:"text-gray-400"},Le={class:"text-green-400 font-medium"},Ie={key:2,class:"text-center py-4 text-gray-400 text-sm"},Qe={key:1,class:"text-sm text-gray-400 italic text-center py-4 border border-gray-700 rounded-lg"},Ke={key:2,class:"space-y-3"},We=["onUpdate:modelValue","onChange"],Ye=["value"],Ge={class:"block text-xs font-medium text-gray-400 mb-1"},Je={key:0,class:"text-xs text-gray-500"},Xe=["onUpdate:modelValue","max"],Ze={key:0,class:"mt-1 text-xs text-red-400"},et={key:1,class:"mt-1 text-xs text-red-400"},tt=["onUpdate:modelValue"],st={class:"flex items-end"},rt=["onClick"],it={class:"text-right space-y-1"},ot={class:"text-sm font-medium text-gray-300"},dt={class:"text-blue-400"},lt={class:"text-sm font-medium text-gray-300"},nt={class:"text-green-400"},at={class:"text-lg font-bold text-gray-200 border-t border-gray-600 pt-1"},ct={class:"text-yellow-400"},ut={key:0,class:"mt-1 text-sm text-red-400"},pt={key:0,class:"space-y-6 border-t border-gray-700 pt-6"},mt={key:0,class:"mt-1 text-sm text-red-400"},gt={key:1,class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},vt={class:"flex items-center space-x-4"},_t={class:"w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg"},yt={class:"text-sm font-bold text-white"},bt={class:"text-white font-medium"},ft={class:"text-sm text-gray-400"},xt={class:"text-sm text-gray-400"},ht={class:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-700"},wt=["disabled"],kt={key:0,class:"flex items-center"},Ct={key:1},St={__name:"RepairOrderModal",props:{show:{type:Boolean,default:!1},repairOrder:{type:Object,default:null},customers:{type:Array,default:()=>[]},devices:{type:Array,default:()=>[]},services:{type:Array,default:()=>[]},technicians:{type:Array,default:()=>[]},preselectedCustomer:{type:[String,Number],default:null},parts:{type:Array,default:()=>[]}},emits:["close","saved"],setup(C,{emit:$}){const a=C,q=$,s=X({customer_id:"",device_id:"",service_id:"",services:[{service_id:"",service_price:"",service_notes:""}],technician_id:"",priority:"medium",issue_description:"",diagnosis:"",solution:"",status:"pending",labor_cost:0,parts_cost:0,customer_notes:"",internal_notes:"",estimated_completion:"",selected_parts:[]}),g=S(!1),v=S(null),z=U(()=>s.customer_id?a.devices.filter(i=>i.customer_id==s.customer_id):[]),A=U(()=>{if(!s.device_id)return a.services;const i=a.devices.find(e=>e.id==s.device_id);return!i||!i.device_type_id?a.services:a.services.filter(e=>e.device_type_id==i.device_type_id)}),D=U(()=>{if(!s.device_id)return a.parts;const i=a.devices.find(e=>e.id==s.device_id);return i?a.parts.filter(e=>e.device_id==i.id||e.compatible_devices&&e.compatible_devices.includes(i.id)||e.compatible_devices&&i.device_type_id&&e.compatible_devices.includes(i.device_type_id)):a.parts});V(()=>a.show,i=>{i&&(a.repairOrder?(g.value=!0,s.customer_id=a.repairOrder.customer_id||"",s.device_id=a.repairOrder.device_id||"",s.service_id=a.repairOrder.service_id||"",a.repairOrder.service_id&&(s.services=[{service_id:a.repairOrder.service_id,service_price:a.repairOrder.labor_cost||"",service_notes:""}]),s.technician_id=a.repairOrder.technician_selection_id||"",s.priority=a.repairOrder.priority||"medium",s.issue_description=a.repairOrder.issue_description||"",s.diagnosis=a.repairOrder.diagnosis||"",s.solution=a.repairOrder.solution||"",s.status=a.repairOrder.status||"pending",s.labor_cost=a.repairOrder.labor_cost||0,s.parts_cost=a.repairOrder.parts_cost||0,s.customer_notes=a.repairOrder.customer_notes||"",s.internal_notes=a.repairOrder.internal_notes||"",s.estimated_completion=a.repairOrder.estimated_completion||"",s.selected_parts=a.repairOrder.parts?.map(e=>({part_id:e.id,quantity:e.pivot.quantity_used,unit_price:e.pivot.unit_price}))||[]):(g.value=!1,s.reset(),a.preselectedCustomer&&(s.customer_id=a.preselectedCustomer)))}),V(()=>s.customer_id,i=>{i?(v.value=a.customers.find(e=>e.id==i),g.value||(s.device_id="")):(v.value=null,s.device_id="")});const _=S([]),P=S(!1);V(()=>s.device_id,async i=>{if(i){s.services=[{service_id:"",service_price:"",service_notes:""}],g.value||(s.selected_parts=[]),P.value=!0;try{const r=await(await fetch(`/devices/${i}/parts`)).json();_.value=[...r.device_parts.map(u=>({...u,suggested:!0,type:"device-specific"})),...r.compatible_parts.map(u=>({...u,suggested:!0,type:"compatible"}))]}catch(e){console.error("Error fetching device parts:",e),_.value=[]}finally{P.value=!1}}else _.value=[],g.value||(s.selected_parts=[])});const N=()=>{s.services.push({service_id:"",service_price:"",service_notes:""})},T=i=>{s.services.length>1&&s.services.splice(i,1)},j=i=>{const e=A.value.find(r=>r.id==i);return e?e.base_price:0},E=(i,e)=>{e&&(s.services[i].service_price=j(e),i===0&&(s.service_id=e,s.labor_cost=j(e)))},H=()=>{s.services.length>0&&s.services[0].service_id&&(s.service_id=s.services[0].service_id,s.labor_cost=parseFloat(s.services[0].service_price)||0),s.parts_cost=s.selected_parts.reduce((i,e)=>i+(parseFloat(e.unit_price)||0)*(parseInt(e.quantity)||0),0)};V(()=>s.services,i=>{i.length>0&&i[0].service_id&&(s.service_id=i[0].service_id,s.labor_cost=parseFloat(i[0].service_price)||0)},{deep:!0});const L=()=>{if(!s){console.error("Form object is not initialized");return}if(H(),(s.selected_parts||[]).filter(r=>r.part_id&&!O(r.part_id,r.quantity)).length>0){window.toast?window.toast.error("Please fix part quantity issues before submitting."):alert("Please fix part quantity issues before submitting.");return}if((s.selected_parts||[]).filter(r=>r.part_id&&w(r.part_id)===0).length>0){window.toast?window.toast.error("Cannot add parts that are out of stock."):alert("Cannot add parts that are out of stock.");return}g.value?s.put(route("repair-orders.update",a.repairOrder.id),{onSuccess:()=>{window.toast?window.toast.success("Repair order updated successfully!"):alert("Repair order updated successfully!"),q("saved"),k()},onError:r=>{console.error("Repair order update failed:",r),window.toast?window.toast.error("Failed to update repair order. Please try again."):alert("Failed to update repair order. Please try again.")}}):s.post(route("repair-orders.store"),{onSuccess:()=>{window.toast?window.toast.success("Repair order created successfully!"):alert("Repair order created successfully!"),q("saved"),k()},onError:r=>{console.error("Repair order creation failed:",r),window.toast?window.toast.error("Failed to create repair order. Please try again."):alert("Failed to create repair order. Please try again.")}})},k=()=>{s&&(s.reset(),s.clearErrors()),v.value=null,q("close")},I=()=>{s.selected_parts.push({part_id:"",quantity:1,unit_price:0})},Q=i=>{const e=s.selected_parts.find(r=>r.part_id==i.id);e?e.quantity=parseInt(e.quantity)+1:s.selected_parts.push({part_id:i.id,quantity:1,unit_price:i.selling_price})},K=()=>{_.value.forEach(i=>{s.selected_parts.find(r=>r.part_id==i.id)||s.selected_parts.push({part_id:i.id,quantity:1,unit_price:i.selling_price})})},W=i=>{s.selected_parts.splice(i,1)},Y=i=>{const e=a.parts.find(r=>r.id==s.selected_parts[i].part_id);e&&(s.selected_parts[i].unit_price=e.selling_price)},F=()=>s.selected_parts.reduce((i,e)=>i+parseFloat(e.unit_price||0)*parseInt(e.quantity||0),0),M=()=>s.services.reduce((i,e)=>i+parseFloat(e.service_price||0),0),G=()=>M()+F(),w=i=>{const e=a.parts.find(r=>r.id==i);return e?e.quantity_in_stock:0},O=(i,e)=>{const r=w(i);return e<=r},J=i=>{const e={low:"bg-green-100 text-green-800 border-green-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",high:"bg-orange-100 text-orange-800 border-orange-200",urgent:"bg-red-100 text-red-800 border-red-200"};return e[i]||e.medium};return(i,e)=>C.show?(l(),d("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:k},[t("div",ee,[e[44]||(e[44]=t("div",{class:"fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"},null,-1)),t("div",{class:"inline-block w-full max-w-5xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-2xl rounded-2xl",onClick:e[8]||(e[8]=R(()=>{},["stop"]))},[t("div",te,[t("div",se,[e[9]||(e[9]=t("div",{class:"p-2 bg-green-600 rounded-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),t("div",null,[t("h3",re,n(g.value?"Edit Repair Order":"Create New Repair Order"),1),t("p",ie,n(g.value?"Update repair order information":"Create a new service request"),1)])]),t("button",{onClick:k,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},e[10]||(e[10]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",oe,[t("form",{onSubmit:R(L,["prevent"]),class:"space-y-6"},[t("div",de,[t("div",null,[e[12]||(e[12]=t("label",{for:"customer_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Customer * ",-1)),m(t("select",{id:"customer_id","onUpdate:modelValue":e[0]||(e[0]=r=>o(s).customer_id=r),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[e[11]||(e[11]=t("option",{value:"",class:"bg-gray-800 text-white"},"Select customer...",-1)),(l(!0),d(y,null,b(C.customers,r=>(l(),d("option",{key:r.id,value:r.id,class:"bg-gray-800 text-white"},n(r.full_name),9,le))),128))],512),[[f,o(s).customer_id]]),o(s).errors.customer_id?(l(),d("div",ne,n(o(s).errors.customer_id),1)):p("",!0)]),t("div",null,[e[13]||(e[13]=t("label",{for:"device_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Device * ",-1)),m(t("select",{id:"device_id","onUpdate:modelValue":e[1]||(e[1]=r=>o(s).device_id=r),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",disabled:!o(s).customer_id,required:""},[t("option",ce,n(o(s).customer_id?"Select device...":"Select customer first"),1),(l(!0),d(y,null,b(z.value,r=>(l(),d("option",{key:r.id,value:r.id,class:"bg-gray-800 text-white"},n(r.brand)+" "+n(r.model)+" ("+n(r.device_type?.name)+") ",9,ue))),128))],8,ae),[[f,o(s).device_id]]),o(s).errors.device_id?(l(),d("div",pe,n(o(s).errors.device_id),1)):p("",!0)])]),t("div",me,[t("div",{class:"flex items-center justify-between mb-4"},[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-300"},"Services Required *",-1)),t("button",{type:"button",onClick:N,class:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-sm transition-colors duration-200 flex items-center space-x-1"},e[14]||(e[14]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),t("span",null,"Add Service",-1)]))]),(l(!0),d(y,null,b(o(s).services,(r,u)=>(l(),d("div",{key:u,class:"mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700"},[t("div",ge,[t("h4",ve,"Service "+n(u+1),1),o(s).services.length>1?(l(),d("button",{key:0,type:"button",onClick:c=>T(u),class:"text-red-400 hover:text-red-300 transition-colors duration-200"},e[16]||(e[16]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,_e)):p("",!0)]),t("div",ye,[t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Type *",-1)),m(t("select",{"onUpdate:modelValue":c=>r.service_id=c,onChange:c=>E(u,r.service_id),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},[e[17]||(e[17]=t("option",{value:""},"Select service...",-1)),(l(!0),d(y,null,b(A.value,c=>(l(),d("option",{key:c.id,value:c.id},n(c.name)+" - ₱"+n(c.base_price),9,fe))),128))],40,be),[[f,r.service_id]]),o(s).errors[`services.${u}.service_id`]?(l(),d("div",xe,n(o(s).errors[`services.${u}.service_id`]),1)):p("",!0)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Price",-1)),m(t("input",{type:"number",step:"0.01",min:"0","onUpdate:modelValue":c=>r.service_price=c,onInput:c=>u===0&&(o(s).labor_cost=parseFloat(r.service_price)||0),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Custom price (optional)"},null,40,he),[[x,r.service_price]]),o(s).errors[`services.${u}.service_price`]?(l(),d("div",we,n(o(s).errors[`services.${u}.service_price`]),1)):p("",!0)])]),t("div",ke,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Service Notes",-1)),m(t("textarea",{"onUpdate:modelValue":c=>r.service_notes=c,rows:"2",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500",placeholder:"Any specific notes for this service..."},null,8,Ce),[[x,r.service_notes]])])]))),128))]),t("div",null,[e[22]||(e[22]=t("label",{for:"technician_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Assigned Technician ",-1)),m(t("select",{id:"technician_id","onUpdate:modelValue":e[2]||(e[2]=r=>o(s).technician_id=r),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},[e[21]||(e[21]=t("option",{value:"",class:"bg-gray-800 text-white"},"Assign later...",-1)),(l(!0),d(y,null,b(C.technicians,r=>(l(),d("option",{key:r.id,value:r.id,class:"bg-gray-800 text-white"},n(r.user?.name)+" ("+n(r.specialization)+") ",9,Pe))),128))],512),[[f,o(s).technician_id]]),o(s).errors.technician_id?(l(),d("div",Se,n(o(s).errors.technician_id),1)):p("",!0)]),t("div",Ve,[t("div",null,[e[24]||(e[24]=t("label",{for:"priority",class:"block text-sm font-medium text-gray-300 mb-2"}," Priority Level * ",-1)),m(t("select",{id:"priority","onUpdate:modelValue":e[3]||(e[3]=r=>o(s).priority=r),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},e[23]||(e[23]=[t("option",{value:"low",class:"bg-gray-800 text-white"},"Low Priority",-1),t("option",{value:"medium",class:"bg-gray-800 text-white"},"Medium Priority",-1),t("option",{value:"high",class:"bg-gray-800 text-white"},"High Priority",-1),t("option",{value:"urgent",class:"bg-gray-800 text-white"},"Urgent",-1)]),512),[[f,o(s).priority]]),t("div",qe,[t("span",{class:B(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",J(o(s).priority)])},n(o(s).priority.toUpperCase()),3)])]),t("div",null,[e[25]||(e[25]=t("label",{for:"estimated_completion",class:"block text-sm font-medium text-gray-300 mb-2"}," Estimated Completion ",-1)),m(t("input",{id:"estimated_completion","onUpdate:modelValue":e[4]||(e[4]=r=>o(s).estimated_completion=r),type:"date",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},null,512),[[x,o(s).estimated_completion]]),o(s).errors.estimated_completion?(l(),d("div",Oe,n(o(s).errors.estimated_completion),1)):p("",!0)])]),t("div",null,[e[26]||(e[26]=t("label",{for:"issue_description",class:"block text-sm font-medium text-gray-300 mb-2"}," Issue Description * ",-1)),m(t("textarea",{id:"issue_description","onUpdate:modelValue":e[5]||(e[5]=r=>o(s).issue_description=r),rows:"4",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Describe the issue or problem with the device...",required:""},null,512),[[x,o(s).issue_description]]),o(s).errors.issue_description?(l(),d("div",Ue,n(o(s).errors.issue_description),1)):p("",!0)]),t("div",null,[t("div",{class:"flex items-center justify-between mb-4"},[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-300"}," Parts Required (Optional) ",-1)),t("button",{type:"button",onClick:I,class:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200 flex items-center space-x-1"},e[27]||(e[27]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),t("span",null,"Add Part",-1)]))]),o(s).device_id&&(_.value.length>0||P.value)?(l(),d("div",Ae,[t("div",je,[e[29]||(e[29]=t("h4",{class:"text-sm font-medium text-gray-300 flex items-center"},[t("svg",{class:"w-4 h-4 mr-2 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),h(" Suggested Parts for This Device ")],-1)),_.value.length>0?(l(),d("button",{key:0,type:"button",onClick:K,class:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors duration-200"}," Add All ")):p("",!0)]),P.value?(l(),d("div",Fe,e[30]||(e[30]=[t("div",{class:"inline-flex items-center text-gray-400"},[t("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),h(" Loading suggested parts... ")],-1)]))):_.value.length>0?(l(),d("div",Me,[(l(!0),d(y,null,b(_.value,r=>(l(),d("div",{key:r.id,class:"bg-gray-800 border border-gray-700 rounded-lg p-3 hover:border-green-500 transition-colors duration-200"},[t("div",Re,[t("div",Be,[t("span",$e,n(r.name),1),r.type==="device-specific"?(l(),d("span",ze," Device Specific ")):(l(),d("span",De," Compatible "))]),t("button",{type:"button",onClick:u=>Q(r),class:"bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"}," Add ",8,Ne)]),t("div",Te,n(r.part_number),1),t("div",Ee,[t("span",He,"Stock: "+n(r.quantity_in_stock),1),t("span",Le,"₱"+n(parseFloat(r.selling_price||0).toFixed(2)),1)])]))),128))])):(l(),d("div",Ie," No specific parts found for this device. You can still add parts manually below. "))])):p("",!0),o(s).selected_parts.length===0?(l(),d("div",Qe,' No parts selected. Click "Add Part" to include parts in this repair order. ')):(l(),d("div",Ke,[(l(!0),d(y,null,b(o(s).selected_parts,(r,u)=>(l(),d("div",{key:u,class:"grid grid-cols-1 md:grid-cols-4 gap-3 p-3 bg-gray-800 rounded-lg border border-gray-700"},[t("div",null,[e[32]||(e[32]=t("label",{class:"block text-xs font-medium text-gray-400 mb-1"},"Part",-1)),m(t("select",{"onUpdate:modelValue":c=>r.part_id=c,onChange:c=>Y(u),class:"w-full rounded bg-gray-700 border-gray-600 text-white text-sm focus:border-red-500 focus:ring-red-500",required:""},[e[31]||(e[31]=t("option",{value:""},"Select part...",-1)),(l(!0),d(y,null,b(D.value,c=>(l(),d("option",{key:c.id,value:c.id},n(c.name)+" ("+n(c.part_number)+") ",9,Ye))),128))],40,We),[[f,r.part_id]])]),t("div",null,[t("label",Ge,[e[33]||(e[33]=h(" Quantity ",-1)),r.part_id?(l(),d("span",Je," (Stock: "+n(w(r.part_id))+") ",1)):p("",!0)]),m(t("input",{"onUpdate:modelValue":c=>r.quantity=c,type:"number",min:"1",max:r.part_id?w(r.part_id):void 0,class:B(["w-full rounded bg-gray-700 border text-white text-sm focus:ring-red-500 transition-colors duration-200",r.part_id&&!O(r.part_id,r.quantity)?"border-red-500 focus:border-red-500":"border-gray-600 focus:border-red-500"]),required:""},null,10,Xe),[[x,r.quantity]]),r.part_id&&!O(r.part_id,r.quantity)?(l(),d("div",Ze," Insufficient stock! Available: "+n(w(r.part_id)),1)):p("",!0),r.part_id&&w(r.part_id)===0?(l(),d("div",et," ⚠️ This part is out of stock! ")):p("",!0)]),t("div",null,[e[34]||(e[34]=t("label",{class:"block text-xs font-medium text-gray-400 mb-1"},"Unit Price (₱)",-1)),m(t("input",{"onUpdate:modelValue":c=>r.unit_price=c,type:"number",step:"0.01",min:"0",class:"w-full rounded bg-gray-700 border-gray-600 text-white text-sm focus:border-red-500 focus:ring-red-500",required:""},null,8,tt),[[x,r.unit_price]])]),t("div",st,[t("button",{type:"button",onClick:c=>W(u),class:"w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors duration-200"}," Remove ",8,rt)])]))),128)),t("div",it,[t("div",ot,[e[35]||(e[35]=h(" Services Cost: ",-1)),t("span",dt,"₱"+n(M().toFixed(2)),1)]),t("div",lt,[e[36]||(e[36]=h(" Parts Cost: ",-1)),t("span",nt,"₱"+n(F().toFixed(2)),1)]),t("div",at,[e[37]||(e[37]=h(" Total Cost: ",-1)),t("span",ct,"₱"+n(G().toFixed(2)),1)])])]))]),t("div",null,[e[38]||(e[38]=t("label",{for:"customer_notes",class:"block text-sm font-medium text-gray-300 mb-2"}," Customer Notes ",-1)),m(t("textarea",{id:"customer_notes","onUpdate:modelValue":e[6]||(e[6]=r=>o(s).customer_notes=r),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Additional notes from the customer..."},null,512),[[x,o(s).customer_notes]]),o(s).errors.customer_notes?(l(),d("div",ut,n(o(s).errors.customer_notes),1)):p("",!0)]),g.value?(l(),d("div",pt,[e[41]||(e[41]=t("h4",{class:"text-lg font-medium text-white mb-4"},"Repair Details",-1)),t("div",null,[e[40]||(e[40]=t("label",{for:"status",class:"block text-sm font-medium text-gray-300 mb-2"}," Status * ",-1)),m(t("select",{id:"status","onUpdate:modelValue":e[7]||(e[7]=r=>o(s).status=r),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},e[39]||(e[39]=[Z('<option value="pending" class="bg-gray-800 text-white">Pending</option><option value="in_progress" class="bg-gray-800 text-white">In Progress</option><option value="waiting_parts" class="bg-gray-800 text-white">Waiting for Parts</option><option value="completed" class="bg-gray-800 text-white">Completed</option><option value="cancelled" class="bg-gray-800 text-white">Cancelled</option><option value="delivered" class="bg-gray-800 text-white">Delivered</option>',6)]),512),[[f,o(s).status]]),o(s).errors.status?(l(),d("div",mt,n(o(s).errors.status),1)):p("",!0)])])):p("",!0),v.value?(l(),d("div",gt,[e[42]||(e[42]=t("h4",{class:"text-sm font-medium text-gray-300 mb-2"},"Customer Information",-1)),t("div",vt,[t("div",_t,[t("span",yt,n(v.value.first_name?.charAt(0))+n(v.value.last_name?.charAt(0)),1)]),t("div",null,[t("p",bt,n(v.value.full_name),1),t("p",ft,n(v.value.email),1),t("p",xt,n(v.value.phone),1)])])])):p("",!0),t("div",ht,[t("button",{type:"button",onClick:k,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Cancel "),t("button",{type:"submit",disabled:o(s).processing,class:"px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[o(s).processing?(l(),d("span",kt,[e[43]||(e[43]=t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),h(" "+n(g.value?"Updating...":"Creating..."),1)])):(l(),d("span",Ct,n(g.value?"Update Order":"Create Repair Order"),1))],8,wt)])],32)])])])])):p("",!0)}};export{St as _};
