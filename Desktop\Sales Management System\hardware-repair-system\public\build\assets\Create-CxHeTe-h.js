import{u as x,g as d,o as l,a as g,d as s,h as y,w as b,b as r,e as f,j as i,i as a,v as n,t as m,F as c}from"./app-BGh5SFxi.js";import{_ as w}from"./AuthenticatedLayout-D7wPPUcl.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const v={class:"p-6"},k={class:"max-w-2xl mx-auto"},_={class:"bg-gray-900 border border-gray-800 rounded-lg p-6"},h={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},C={key:0,class:"mt-2 text-sm text-red-400"},V={key:0,class:"mt-2 text-sm text-red-400"},U={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},N={key:0,class:"mt-2 text-sm text-red-400"},z={key:0,class:"mt-2 text-sm text-red-400"},q={key:0,class:"mt-2 text-sm text-red-400"},F={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},A={key:0,class:"mt-2 text-sm text-red-400"},B={key:0,class:"mt-2 text-sm text-red-400"},S={key:0,class:"mt-2 text-sm text-red-400"},$={key:0,class:"mt-2 text-sm text-red-400"},j={class:"flex items-center justify-end space-x-4"},D=["disabled"],E={key:0},M={key:1},G={__name:"Create",setup(P){const t=x({first_name:"",last_name:"",email:"",phone:"",address:"",city:"",state:"",zip_code:"",notes:""}),p=()=>{t.post(route("customers.store"))};return(u,e)=>(l(),d(c,null,[g(s(y),{title:"Add New Customer"}),g(w,null,{header:b(()=>e[10]||(e[10]=[r("h2",{class:"text-xl font-semibold leading-tight text-white"}," Add New Customer ",-1)])),default:b(()=>[r("div",v,[r("div",k,[r("div",_,[r("form",{onSubmit:f(p,["prevent"]),class:"space-y-6"},[r("div",h,[r("div",null,[e[11]||(e[11]=r("label",{for:"first_name",class:"block text-sm font-medium text-gray-300"},"First Name",-1)),i(r("input",{id:"first_name","onUpdate:modelValue":e[0]||(e[0]=o=>s(t).first_name=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},null,512),[[n,s(t).first_name]]),s(t).errors.first_name?(l(),d("div",C,m(s(t).errors.first_name),1)):a("",!0)]),r("div",null,[e[12]||(e[12]=r("label",{for:"last_name",class:"block text-sm font-medium text-gray-300"},"Last Name",-1)),i(r("input",{id:"last_name","onUpdate:modelValue":e[1]||(e[1]=o=>s(t).last_name=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},null,512),[[n,s(t).last_name]]),s(t).errors.last_name?(l(),d("div",V,m(s(t).errors.last_name),1)):a("",!0)])]),r("div",U,[r("div",null,[e[13]||(e[13]=r("label",{for:"email",class:"block text-sm font-medium text-gray-300"},"Email",-1)),i(r("input",{id:"email","onUpdate:modelValue":e[2]||(e[2]=o=>s(t).email=o),type:"email",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},null,512),[[n,s(t).email]]),s(t).errors.email?(l(),d("div",N,m(s(t).errors.email),1)):a("",!0)]),r("div",null,[e[14]||(e[14]=r("label",{for:"phone",class:"block text-sm font-medium text-gray-300"},"Phone",-1)),i(r("input",{id:"phone","onUpdate:modelValue":e[3]||(e[3]=o=>s(t).phone=o),type:"tel",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500",required:""},null,512),[[n,s(t).phone]]),s(t).errors.phone?(l(),d("div",z,m(s(t).errors.phone),1)):a("",!0)])]),r("div",null,[e[15]||(e[15]=r("label",{for:"address",class:"block text-sm font-medium text-gray-300"},"Address",-1)),i(r("input",{id:"address","onUpdate:modelValue":e[4]||(e[4]=o=>s(t).address=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[n,s(t).address]]),s(t).errors.address?(l(),d("div",q,m(s(t).errors.address),1)):a("",!0)]),r("div",F,[r("div",null,[e[16]||(e[16]=r("label",{for:"city",class:"block text-sm font-medium text-gray-300"},"City",-1)),i(r("input",{id:"city","onUpdate:modelValue":e[5]||(e[5]=o=>s(t).city=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[n,s(t).city]]),s(t).errors.city?(l(),d("div",A,m(s(t).errors.city),1)):a("",!0)]),r("div",null,[e[17]||(e[17]=r("label",{for:"state",class:"block text-sm font-medium text-gray-300"},"State",-1)),i(r("input",{id:"state","onUpdate:modelValue":e[6]||(e[6]=o=>s(t).state=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[n,s(t).state]]),s(t).errors.state?(l(),d("div",B,m(s(t).errors.state),1)):a("",!0)]),r("div",null,[e[18]||(e[18]=r("label",{for:"zip_code",class:"block text-sm font-medium text-gray-300"},"ZIP Code",-1)),i(r("input",{id:"zip_code","onUpdate:modelValue":e[7]||(e[7]=o=>s(t).zip_code=o),type:"text",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[n,s(t).zip_code]]),s(t).errors.zip_code?(l(),d("div",S,m(s(t).errors.zip_code),1)):a("",!0)])]),r("div",null,[e[19]||(e[19]=r("label",{for:"notes",class:"block text-sm font-medium text-gray-300"},"Notes",-1)),i(r("textarea",{id:"notes","onUpdate:modelValue":e[8]||(e[8]=o=>s(t).notes=o),rows:"3",class:"mt-1 block w-full rounded-md bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[n,s(t).notes]]),s(t).errors.notes?(l(),d("div",$,m(s(t).errors.notes),1)):a("",!0)]),r("div",j,[r("button",{type:"button",onClick:e[9]||(e[9]=o=>u.$inertia.visit(u.route("customers.index"))),class:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-800 transition-colors duration-200"}," Cancel "),r("button",{type:"submit",disabled:s(t).processing,class:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors duration-200"},[s(t).processing?(l(),d("span",E,"Creating...")):(l(),d("span",M,"Create Customer"))],8,D)])],32)])])])]),_:1})],64))}};export{G as default};
