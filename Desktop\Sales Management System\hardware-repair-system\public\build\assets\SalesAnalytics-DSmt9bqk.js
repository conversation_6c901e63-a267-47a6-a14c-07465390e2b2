import{r as j,m as r,g as d,o as c,a as m,d as g,h as S,w as p,b as t,t as a,i as D,F as h,y as f,l as P,j as A,I as B}from"./app-BGh5SFxi.js";import{_ as L}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as y}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const V={class:"flex justify-between items-center"},F={class:"flex items-center space-x-4"},N={class:"flex items-center space-x-4"},O=["value"],T={class:"py-12"},z={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},U={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},R={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},I={class:"flex items-center"},H={class:"ml-4"},E={class:"text-2xl font-semibold text-white"},Y={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},$={class:"flex items-center"},q={class:"ml-4"},G={class:"text-2xl font-semibold text-white"},J={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},K={class:"flex items-center"},Q={class:"ml-4"},W={class:"text-2xl font-semibold text-white"},X={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-white"},st={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ot={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},at={class:"p-6"},lt={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},it={class:"p-6"},nt={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},rt={class:"p-6"},dt={class:"overflow-x-auto"},ct={class:"min-w-full divide-y divide-gray-700"},mt={class:"bg-gray-800 divide-y divide-gray-700"},ut={class:"px-6 py-4 whitespace-nowrap"},pt={class:"text-sm font-medium text-white capitalize"},ht={class:"px-6 py-4 whitespace-nowrap"},xt={class:"text-sm font-medium text-green-400"},gt={class:"px-6 py-4 whitespace-nowrap"},ft={class:"text-sm text-gray-300"},yt={class:"px-6 py-4 whitespace-nowrap"},vt={class:"text-sm text-gray-300"},wt={class:"px-6 py-4 whitespace-nowrap"},_t={class:"text-sm text-gray-300"},bt={key:0},Pt={__name:"SalesAnalytics",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(i){const l=i,n=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),v=s=>parseFloat(s||0).toLocaleString("en-US"),w=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],u=j(l.period),_=()=>{window.location.href=route("reports.sales-analytics",{period:u.value})},b=r(()=>({type:"line",data:{labels:l.charts.sales_trend?.map(s=>new Date(s.date).toLocaleDateString())||[],datasets:[{label:"Sales Revenue",data:l.charts.sales_trend?.map(s=>s.sales)||[],borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.1)",tension:.4,fill:!0,yAxisID:"y"},{label:"Number of Orders",data:l.charts.sales_trend?.map(s=>s.orders)||[],borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!1,yAxisID:"y1"}]},options:{responsive:!0,interaction:{mode:"index",intersect:!1},plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{type:"linear",display:!0,position:"left",ticks:{color:"#9ca3af"}},y1:{type:"linear",display:!0,position:"right",ticks:{color:"#9ca3af"},grid:{drawOnChartArea:!1}}}}})),k=r(()=>({type:"doughnut",data:{labels:l.charts.payment_methods?.map(s=>s.payment_method||"Unknown")||[],datasets:[{data:l.charts.payment_methods?.map(s=>s.total)||[],backgroundColor:["#10b981","#3b82f6","#f59e0b","#ef4444","#8b5cf6","#06b6d4"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}})),x=r(()=>l.metrics.total_sales||0),C=r(()=>(l.metrics.pending_amount||0)+(l.metrics.partially_paid_amount||0)),M=r(()=>{const s=x.value+C.value;return s>0?x.value/s*100:0});return(s,e)=>(c(),d(h,null,[m(g(S),{title:"Sales Analytics"}),m(L,null,{header:p(()=>[t("div",V,[t("div",F,[m(g(P),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:p(()=>e[1]||(e[1]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),e[2]||(e[2]=t("h2",{class:"font-semibold text-xl text-white leading-tight"}," Sales Analytics ",-1))]),t("div",N,[A(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>u.value=o),onChange:_,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(c(),d(h,null,f(w,o=>t("option",{key:o.value,value:o.value},a(o.label),9,O)),64))],544),[[B,u.value]])])])]),default:p(()=>[t("div",T,[t("div",z,[t("div",U,[t("div",R,[t("div",I,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",H,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Sales",-1)),t("p",E,a(n(i.metrics.total_sales)),1)])])]),t("div",Y,[t("div",$,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",q,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-400"},"Pending Amount",-1)),t("p",G,a(n(i.metrics.pending_amount)),1)])])]),t("div",J,[t("div",K,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})])])],-1)),t("div",Q,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-400"},"Partially Paid",-1)),t("p",W,a(n(i.metrics.partially_paid_amount)),1)])])]),t("div",X,[t("div",Z,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",tt,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-400"},"Collection Rate",-1)),t("p",et,a(M.value.toFixed(1))+"%",1)])])])]),t("div",st,[t("div",ot,[t("div",at,[e[11]||(e[11]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Sales Trend",-1)),m(y,{config:b.value},null,8,["config"])])]),t("div",lt,[t("div",it,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Payment Methods Distribution",-1)),m(y,{config:k.value},null,8,["config"])])])]),t("div",nt,[t("div",rt,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-white mb-6"},"Payment Methods Breakdown",-1)),t("div",dt,[t("table",ct,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Payment Method "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Total Amount "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Transaction Count "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Average Transaction "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Percentage ")])],-1)),t("tbody",mt,[(c(!0),d(h,null,f(i.metrics.payment_methods,o=>(c(),d("tr",{key:o.payment_method,class:"hover:bg-gray-700"},[t("td",ut,[t("div",pt,a(o.payment_method||"Unknown"),1)]),t("td",ht,[t("div",xt,a(n(o.total)),1)]),t("td",gt,[t("div",ft,a(v(o.count)),1)]),t("td",yt,[t("div",vt,a(n(o.total/o.count)),1)]),t("td",wt,[t("div",_t,a((o.total/i.metrics.total_sales*100).toFixed(1))+"% ",1)])]))),128)),!i.metrics.payment_methods||i.metrics.payment_methods.length===0?(c(),d("tr",bt,e[13]||(e[13]=[t("td",{colspan:"5",class:"px-6 py-4 text-center text-gray-400"}," No payment data available for the selected period. ",-1)]))):D("",!0)])])])])])])])]),_:1})],64))}};export{Pt as default};
