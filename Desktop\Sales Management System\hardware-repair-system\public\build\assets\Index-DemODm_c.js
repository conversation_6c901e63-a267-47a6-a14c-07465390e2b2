import{u as T,r as b,x as G,g as d,i as p,o as l,b as e,e as L,t as c,j as k,v as $,d as o,I,F as M,y as z,k as Q,f as P,m as ee,n as q,q as D,a as B,h as te,w as A,l as E,c as U}from"./app-BGh5SFxi.js";import{_ as se}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as re}from"./ConfirmationModal-Bx9j458V.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const oe={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},ie={class:"flex items-center justify-between mb-6"},le={class:"flex items-center space-x-3"},ae={class:"text-xl font-bold text-white"},ne={class:"text-sm text-gray-400"},de={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={key:0,class:"mt-1 text-sm text-red-400"},ve=["value"],ge={key:0,class:"mt-1 text-sm text-red-400"},me={class:"mt-6"},pe={key:0,class:"mt-1 text-sm text-red-400"},xe={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},fe={class:"relative"},be={key:0,class:"mt-1 text-sm text-red-400"},we={key:0,class:"mt-1 text-sm text-red-400"},he={key:0,class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},ke={class:"flex items-center"},_e={class:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-700"},Ce=["disabled"],$e={key:0,class:"flex items-center"},Me={key:1},je={__name:"ServiceModal",props:{show:{type:Boolean,default:!1},service:{type:Object,default:null},deviceTypes:{type:Array,default:()=>[]}},emits:["close","saved"],setup(u,{emit:C}){const x=u,w=C,r=T({name:"",description:"",device_type_id:"",base_price:"",estimated_duration:"",is_active:!0}),a=b(!1);G(()=>x.show,g=>{g&&(x.service?(a.value=!0,r.name=x.service.name||"",r.description=x.service.description||"",r.device_type_id=x.service.device_type_id||"",r.base_price=x.service.base_price||"",r.estimated_duration=x.service.estimated_duration||"",r.is_active=x.service.is_active??!0):(a.value=!1,r.reset(),r.is_active=!0))});const _=()=>{a.value?r.put(route("services.update",x.service.id),{onSuccess:()=>{window.toast&&window.toast.success("Service updated successfully!"),w("saved"),h()},onError:g=>{console.error("Service update failed:",g),window.toast&&window.toast.error("Failed to update service. Please try again.")}}):r.post(route("services.store"),{onSuccess:()=>{window.toast&&window.toast.success("Service created successfully!"),w("saved"),h()},onError:g=>{console.error("Service creation failed:",g),window.toast&&window.toast.error("Failed to create service. Please try again.")}})},h=()=>{r.reset(),r.clearErrors(),w("close")};return(g,s)=>u.show?(l(),d("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:h},[e("div",oe,[s[23]||(s[23]=e("div",{class:"fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"},null,-1)),e("div",{class:"inline-block w-full max-w-3xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-2xl rounded-2xl",onClick:s[6]||(s[6]=L(()=>{},["stop"]))},[e("div",ie,[e("div",le,[s[7]||(s[7]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])],-1)),e("div",null,[e("h3",ae,c(a.value?"Edit Service":"Add New Service"),1),e("p",ne,c(a.value?"Update service information":"Create a new repair service"),1)])]),e("button",{onClick:h,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},s[8]||(s[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("form",{onSubmit:L(_,["prevent"]),class:"space-y-6"},[e("div",de,[s[13]||(s[13]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Service Information",-1)),e("div",ce,[e("div",null,[s[9]||(s[9]=e("label",{for:"name",class:"block text-sm font-medium text-gray-300 mb-2"}," Service Name * ",-1)),k(e("input",{id:"name","onUpdate:modelValue":s[0]||(s[0]=v=>o(r).name=v),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Screen Replacement",required:""},null,512),[[$,o(r).name]]),o(r).errors.name?(l(),d("div",ue,c(o(r).errors.name),1)):p("",!0)]),e("div",null,[s[11]||(s[11]=e("label",{for:"device_type_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Device Category * ",-1)),k(e("select",{id:"device_type_id","onUpdate:modelValue":s[1]||(s[1]=v=>o(r).device_type_id=v),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[s[10]||(s[10]=e("option",{value:"",class:"bg-gray-700 text-white"},"Select device category...",-1)),(l(!0),d(M,null,z(u.deviceTypes,v=>(l(),d("option",{key:v.id,value:v.id,class:"bg-gray-700 text-white"},c(v.name),9,ve))),128))],512),[[I,o(r).device_type_id]]),o(r).errors.device_type_id?(l(),d("div",ge,c(o(r).errors.device_type_id),1)):p("",!0)])]),s[14]||(s[14]=e("div",{class:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6"},null,-1)),e("div",me,[s[12]||(s[12]=e("label",{for:"description",class:"block text-sm font-medium text-gray-300 mb-2"}," Description ",-1)),k(e("textarea",{id:"description","onUpdate:modelValue":s[2]||(s[2]=v=>o(r).description=v),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Detailed description of the service..."},null,512),[[$,o(r).description]]),o(r).errors.description?(l(),d("div",pe,c(o(r).errors.description),1)):p("",!0)])]),e("div",xe,[s[18]||(s[18]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Pricing & Duration",-1)),e("div",ye,[e("div",null,[s[16]||(s[16]=e("label",{for:"base_price",class:"block text-sm font-medium text-gray-300 mb-2"}," Base Price * ",-1)),e("div",fe,[s[15]||(s[15]=e("span",{class:"absolute left-3 top-2.5 text-gray-400"},"₱",-1)),k(e("input",{id:"base_price","onUpdate:modelValue":s[3]||(s[3]=v=>o(r).base_price=v),type:"number",step:"0.01",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200 pl-8",placeholder:"999.00",required:""},null,512),[[$,o(r).base_price]])]),o(r).errors.base_price?(l(),d("div",be,c(o(r).errors.base_price),1)):p("",!0)]),e("div",null,[s[17]||(s[17]=e("label",{for:"estimated_duration",class:"block text-sm font-medium text-gray-300 mb-2"}," Estimated Duration (minutes) ",-1)),k(e("input",{id:"estimated_duration","onUpdate:modelValue":s[4]||(s[4]=v=>o(r).estimated_duration=v),type:"number",min:"1",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"60"},null,512),[[$,o(r).estimated_duration]]),o(r).errors.estimated_duration?(l(),d("div",we,c(o(r).errors.estimated_duration),1)):p("",!0)])])]),a.value?(l(),d("div",he,[s[20]||(s[20]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Status",-1)),e("label",ke,[k(e("input",{"onUpdate:modelValue":s[5]||(s[5]=v=>o(r).is_active=v),type:"checkbox",class:"rounded bg-gray-700 border-gray-600 text-red-600 shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[Q,o(r).is_active]]),s[19]||(s[19]=e("span",{class:"ml-2 text-sm text-gray-300"},"Active Service",-1))]),s[21]||(s[21]=e("p",{class:"text-xs text-gray-500 mt-1"},"Inactive services won't be available for new repair orders",-1))])):p("",!0),e("div",_e,[e("button",{type:"button",onClick:h,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Cancel "),e("button",{type:"submit",disabled:o(r).processing,class:"px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[o(r).processing?(l(),d("span",$e,[s[22]||(s[22]=e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),P(" "+c(a.value?"Updating...":"Creating..."),1)])):(l(),d("span",Me,c(a.value?"Update Service":"Create Service"),1))],8,Ce)])],32)])])])):p("",!0)}},Se={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Be={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Ve={class:"inline-block align-bottom bg-gray-900 rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full border border-gray-700"},Ae={class:"flex h-96"},De={class:"w-2/3 border-r border-gray-700"},ze={class:"overflow-y-auto h-80 p-4"},He={key:0,class:"text-center text-gray-400 py-8"},Ee={key:1,class:"space-y-2"},Fe={class:"flex items-center justify-between"},Ne={class:"flex-1"},Ue={class:"flex items-center space-x-2"},Le={class:"text-white font-medium"},Ie={key:0,class:"text-sm text-gray-400 mt-1"},Pe={key:1,class:"text-sm text-gray-300 mt-1"},qe={class:"text-xs text-gray-500 mt-1"},Oe={class:"flex items-center space-x-1"},Te=["onClick"],Ge=["onClick"],Qe={class:"w-1/3"},Re={key:0,class:"p-6 text-center text-gray-400 h-full flex items-center justify-center"},Je={class:"mb-4"},Ke={class:"text-lg font-medium text-white mb-4"},We={class:"space-y-4"},Xe={key:0,class:"mt-1 text-sm text-red-400"},Ye={key:0,class:"mt-1 text-sm text-red-400"},Ze={key:0,class:"mt-1 text-sm text-red-400"},et={class:"flex items-center"},tt={class:"flex items-center justify-end space-x-2 pt-4 mt-6 border-t border-gray-700"},st=["disabled"],rt={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},ot={__name:"DeviceCategoryModal",props:{show:{type:Boolean,default:!1},deviceTypes:{type:Array,default:()=>[]}},emits:["close","saved"],setup(u,{emit:C}){const x=C,w=b(!1),r=b(null),a=T({name:"",category:"",description:"",is_active:!0}),_=ee(()=>!!r.value),h=()=>{r.value=null,a.reset(),w.value=!0},g=y=>{r.value=y,a.name=y.name||"",a.category=y.category||"",a.description=y.description||"",a.is_active=y.is_active??!0,w.value=!0},s=()=>{w.value=!1,r.value=null,a.reset(),a.clearErrors()},v=()=>{_.value?a.put(route("device-types.update",r.value.id),{onSuccess:()=>{window.toast&&window.toast.success("Device category updated successfully!"),s(),x("saved")},onError:y=>{window.toast&&window.toast.error(y.message||"Failed to update device category")}}):a.post(route("device-types.store"),{onSuccess:()=>{window.toast&&window.toast.success("Device category created successfully!"),s(),x("saved")},onError:y=>{window.toast&&window.toast.error(y.message||"Failed to create device category")}})},j=y=>{confirm("Are you sure you want to delete this device category?")&&D.delete(route("device-types.destroy",y.id),{onSuccess:()=>{window.toast&&window.toast.success("Device category deleted successfully!"),x("saved")},onError:n=>{window.toast&&window.toast.error(n.message||"Failed to delete device category")}})},V=y=>y?"bg-green-900 text-green-300 border-green-600":"bg-red-900 text-red-300 border-red-600",S=()=>{s(),x("close")};return(y,n)=>u.show?(l(),d("div",Se,[e("div",Be,[e("div",{class:"fixed inset-0 bg-black bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:S}),e("div",Ve,[e("div",{class:"bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 border-b border-gray-700"},[e("div",{class:"flex items-center justify-between"},[n[5]||(n[5]=e("h3",{class:"text-xl font-semibold text-white"}," Device Categories Management ",-1)),e("button",{onClick:S,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},n[4]||(n[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",Ae,[e("div",De,[e("div",{class:"p-4 border-b border-gray-700"},[e("div",{class:"flex items-center justify-between"},[n[7]||(n[7]=e("h4",{class:"text-lg font-medium text-white"},"Existing Categories",-1)),e("button",{onClick:h,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-3 py-1.5 rounded-lg transition-all duration-200 text-sm flex items-center space-x-1"},n[6]||(n[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add New",-1)]))])]),e("div",ze,[u.deviceTypes.length===0?(l(),d("div",He,n[8]||(n[8]=[e("svg",{class:"w-12 h-12 mx-auto mb-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("p",null,"No device categories found",-1),e("p",{class:"text-sm mt-1"},'Click "Add New" to create your first category',-1)]))):(l(),d("div",Ee,[(l(!0),d(M,null,z(u.deviceTypes,m=>(l(),d("div",{key:m.id,class:"bg-gray-800 border border-gray-700 rounded-lg p-3 hover:bg-gray-750 transition-colors duration-200"},[e("div",Fe,[e("div",Ne,[e("div",Ue,[e("h5",Le,c(m.name),1),e("span",{class:q(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",V(m.is_active)])},c(m.is_active?"Active":"Inactive"),3)]),m.category?(l(),d("p",Ie,c(m.category),1)):p("",!0),m.description?(l(),d("p",Pe,c(m.description),1)):p("",!0),e("p",qe,c(m.services_count||0)+" services",1)]),e("div",Oe,[e("button",{onClick:F=>g(m),class:"p-1.5 text-blue-400 hover:text-blue-300 hover:bg-gray-700 rounded transition-colors duration-200",title:"Edit"},n[9]||(n[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Te),e("button",{onClick:F=>j(m),class:"p-1.5 text-red-400 hover:text-red-300 hover:bg-gray-700 rounded transition-colors duration-200",title:"Delete"},n[10]||(n[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ge)])])]))),128))]))])]),e("div",Qe,[w.value?(l(),d("form",{key:1,onSubmit:L(v,["prevent"]),class:"p-4 h-full overflow-y-auto"},[e("div",Je,[e("h4",Ke,c(_.value?"Edit Category":"Create New Category"),1)]),e("div",We,[e("div",null,[n[12]||(n[12]=e("label",{for:"name",class:"block text-sm font-medium text-gray-300 mb-2"}," Category Name * ",-1)),k(e("input",{id:"name","onUpdate:modelValue":n[0]||(n[0]=m=>o(a).name=m),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"iOS Device, Android Device, etc.",required:""},null,512),[[$,o(a).name]]),o(a).errors.name?(l(),d("div",Xe,c(o(a).errors.name),1)):p("",!0)]),e("div",null,[n[13]||(n[13]=e("label",{for:"category",class:"block text-sm font-medium text-gray-300 mb-2"}," Category Type ",-1)),k(e("input",{id:"category","onUpdate:modelValue":n[1]||(n[1]=m=>o(a).category=m),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Mobile, Computer, etc."},null,512),[[$,o(a).category]]),o(a).errors.category?(l(),d("div",Ye,c(o(a).errors.category),1)):p("",!0)]),e("div",null,[n[14]||(n[14]=e("label",{for:"description",class:"block text-sm font-medium text-gray-300 mb-2"}," Description ",-1)),k(e("textarea",{id:"description","onUpdate:modelValue":n[2]||(n[2]=m=>o(a).description=m),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Brief description..."},null,512),[[$,o(a).description]]),o(a).errors.description?(l(),d("div",Ze,c(o(a).errors.description),1)):p("",!0)]),e("div",et,[k(e("input",{id:"is_active","onUpdate:modelValue":n[3]||(n[3]=m=>o(a).is_active=m),type:"checkbox",class:"w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 focus:ring-2"},null,512),[[Q,o(a).is_active]]),n[15]||(n[15]=e("label",{for:"is_active",class:"ml-2 text-sm text-gray-300"}," Active (available for use) ",-1))])]),e("div",tt,[e("button",{type:"button",onClick:s,class:"px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:o(a).processing,class:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"},[o(a).processing?(l(),d("svg",rt,n[16]||(n[16]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):p("",!0),e("span",null,c(o(a).processing?"Saving...":_.value?"Update":"Create"),1)],8,st)])],32)):(l(),d("div",Re,n[11]||(n[11]=[e("div",null,[e("svg",{class:"w-16 h-16 mx-auto mb-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})]),e("p",{class:"text-lg font-medium"},"Add New Category"),e("p",{class:"text-sm mt-2"},'Click "Add New" to create a device category')],-1)])))])])])])])):p("",!0)}},it={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},lt={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},at={class:"relative flex-1 max-w-md"},nt={class:"flex items-center space-x-2"},dt=["value"],ct={class:"flex items-center space-x-2"},ut={class:"p-6 space-y-6"},vt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},gt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},mt={class:"flex items-center justify-between"},pt={class:"text-2xl font-bold text-white"},xt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},yt={class:"flex items-center justify-between"},ft={class:"text-2xl font-bold text-white"},bt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},wt={class:"flex items-center justify-between"},ht={class:"text-2xl font-bold text-white"},kt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},_t={class:"flex items-center justify-between"},Ct={class:"text-2xl font-bold text-white"},$t={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},Mt={class:"overflow-x-auto"},jt={class:"min-w-full divide-y divide-gray-700"},St={class:"bg-gray-900 divide-y divide-gray-700"},Bt={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"flex items-center"},At={class:"text-sm font-semibold text-white group-hover:text-red-300 transition-colors duration-200"},Dt={key:0,class:"text-xs text-gray-400"},zt={class:"px-6 py-4 whitespace-nowrap"},Ht={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm"},Et={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Nt={class:"px-6 py-4 whitespace-nowrap"},Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Lt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},It={class:"flex items-center justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},Pt=["onClick"],qt=["onClick"],Ot={key:0},Tt={colspan:"7",class:"px-6 py-12 text-center"},Gt={class:"flex flex-col items-center"},Qt={class:"text-lg font-medium text-white mb-2"},Rt={class:"text-gray-400 text-sm mb-4"},Jt={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},Kt={class:"flex items-center justify-between"},Wt={class:"flex-1 flex justify-between sm:hidden"},Xt={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Yt={class:"text-sm text-gray-400"},Zt={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},es=["innerHTML"],ls={__name:"Index",props:{services:Object,categories:Array,serviceCategories:Array,deviceTypes:Array,filters:Object},setup(u){const C=u,x=b(!1),w=b(!1),r=b(!1),a=b(null),_=b(null),h=b(!1),g=b(C.filters?.search||""),s=b(C.filters?.status||"all"),v=b(C.filters?.category||"all"),j=b(C.filters?.device_type||"all"),V=b(C.filters?.sort||"name"),S=b(C.filters?.direction||"asc");let y;G(g,f=>{clearTimeout(y),y=setTimeout(()=>{H()},300)});const n=()=>{a.value=null,x.value=!0},m=f=>{a.value=f,x.value=!0},F=()=>{w.value=!0},R=f=>{_.value=f,r.value=!0},J=()=>{x.value=!1,D.reload({only:["services"]})},K=()=>{w.value=!1,D.reload({only:["deviceTypes"]})},W=()=>{_.value&&(h.value=!0,D.delete(route("services.destroy",_.value.id),{onSuccess:()=>{r.value=!1,_.value=null,h.value=!1},onError:()=>{h.value=!1}}))},X=()=>{r.value=!1,_.value=null,h.value=!1},H=()=>{const f={search:g.value||void 0,status:s.value!=="all"?s.value:void 0,category:v.value!=="all"?v.value:void 0,device_type:j.value!=="all"?j.value:void 0,sort:V.value!=="name"?V.value:void 0,direction:S.value!=="asc"?S.value:void 0};D.get(route("services.index"),f,{preserveState:!0,replace:!0})},O=()=>{g.value="",s.value="all",v.value="all",j.value="all",V.value="name",S.value="asc",H()},Y=f=>f?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200",Z=f=>{if(!f)return"N/A";if(f<60)return`${f}m`;const t=Math.floor(f/60),i=f%60;return i>0?`${t}h ${i}m`:`${t}h`};return(f,t)=>(l(),d(M,null,[B(o(te),{title:"Services"}),B(se,null,{header:A(()=>[e("div",it,[t[12]||(t[12]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Service Management "),e("p",{class:"text-gray-400 text-sm"},"Manage repair services and pricing")],-1)),e("div",lt,[e("div",at,[k(e("input",{"onUpdate:modelValue":t[0]||(t[0]=i=>g.value=i),type:"text",placeholder:"Search services...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[$,g.value]]),t[7]||(t[7]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),g.value?(l(),d("button",{key:0,onClick:t[1]||(t[1]=i=>g.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):p("",!0)]),e("div",nt,[k(e("select",{"onUpdate:modelValue":t[2]||(t[2]=i=>s.value=i),onChange:H,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[8]||(t[8]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1)]),544),[[I,s.value]]),k(e("select",{"onUpdate:modelValue":t[3]||(t[3]=i=>v.value=i),onChange:H,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[t[9]||(t[9]=e("option",{value:"all"},"All Service Categories",-1)),(l(!0),d(M,null,z(u.categories,i=>(l(),d("option",{key:i,value:i},c(i),9,dt))),128))],544),[[I,v.value]])]),e("div",ct,[g.value||s.value!=="all"||v.value!=="all"||j.value!=="all"?(l(),d("button",{key:0,onClick:O,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):p("",!0),e("button",{onClick:F,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("span",null,"Add Category",-1)])),e("button",{onClick:n,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[11]||(t[11]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Service",-1)]))])])])]),default:A(()=>[e("div",ut,[e("div",vt,[e("div",gt,[e("div",mt,[e("div",null,[t[13]||(t[13]=e("p",{class:"text-sm text-gray-400"},"Total Services",-1)),e("p",pt,c(u.services.total||0),1)]),t[14]||(t[14]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])],-1))])]),e("div",xt,[e("div",yt,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm text-gray-400"},"Active",-1)),e("p",ft,c(u.services.data?.filter(i=>i.is_active).length||0),1)]),t[16]||(t[16]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",bt,[e("div",wt,[e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm text-gray-400"},"Categories",-1)),e("p",ht,c(u.categories.length),1)]),t[18]||(t[18]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),e("div",kt,[e("div",_t,[e("div",null,[t[19]||(t[19]=e("p",{class:"text-sm text-gray-400"},"Avg Price",-1)),e("p",Ct,"₱"+c(Math.round((u.services.data?.reduce((i,N)=>i+parseFloat(N.base_price||0),0)||0)/Math.max(u.services.data?.length||1,1))),1)]),t[20]||(t[20]=e("div",{class:"p-2 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])])]),e("div",$t,[t[30]||(t[30]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Service Catalog")])])],-1)),e("div",Mt,[e("table",jt,[t[27]||(t[27]=e("thead",{class:"bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Service"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Category"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Price"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Duration"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Orders"),e("th",{class:"px-6 py-4 text-right text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",St,[(l(!0),d(M,null,z(u.services.data,i=>(l(),d("tr",{key:i.id,class:"hover:bg-gray-800 transition-colors duration-200 group"},[e("td",Bt,[e("div",Vt,[t[21]||(t[21]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-shadow duration-200"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])],-1)),e("div",null,[e("div",At,c(i.name),1),i.description?(l(),d("div",Dt,c(i.description.substring(0,50))+c(i.description.length>50?"...":""),1)):p("",!0)])])]),e("td",zt,[e("span",Ht,c(i.category),1)]),e("td",Et," ₱"+c(i.base_price),1),e("td",Ft,c(Z(i.estimated_duration)),1),e("td",Nt,[e("span",{class:q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",Y(i.is_active)])},c(i.is_active?"Active":"Inactive"),3)]),e("td",Ut,c(i.repair_orders_count||0),1),e("td",Lt,[e("div",It,[B(o(E),{href:f.route("services.show",i.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Service"},{default:A(()=>t[22]||(t[22]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[22]},1032,["href"]),e("button",{onClick:N=>m(i),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Service"},t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Pt),e("button",{onClick:N=>R(i),class:"p-2 text-red-400 hover:text-red-300 hover:bg-red-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Deactivate Service"},t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"})],-1)]),8,qt)])])]))),128)),u.services.data?.length===0?(l(),d("tr",Ot,[e("td",Tt,[e("div",Gt,[t[26]||(t[26]=e("div",{class:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4"},[e("svg",{class:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])],-1)),e("h3",Qt,c(g.value?"No services found":"No services yet"),1),e("p",Rt,c(g.value?`No services match "${g.value}"`:"Get started by adding your first service"),1),g.value?(l(),d("button",{key:1,onClick:O,class:"text-red-400 hover:text-red-300 text-sm font-medium"}," Clear search and show all services → ")):(l(),d("button",{key:0,onClick:n,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[25]||(t[25]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add your first service",-1)])))])])])):p("",!0)])])]),u.services.links?(l(),d("div",Jt,[e("div",Kt,[e("div",Wt,[u.services.prev_page_url?(l(),U(o(E),{key:0,href:u.services.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:A(()=>t[28]||(t[28]=[P(" Previous ",-1)])),_:1,__:[28]},8,["href"])):p("",!0),u.services.next_page_url?(l(),U(o(E),{key:1,href:u.services.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:A(()=>t[29]||(t[29]=[P(" Next ",-1)])),_:1,__:[29]},8,["href"])):p("",!0)]),e("div",Xt,[e("div",null,[e("p",Yt," Showing "+c(u.services.from)+" to "+c(u.services.to)+" of "+c(u.services.total)+" results ",1)]),e("div",null,[e("nav",Zt,[(l(!0),d(M,null,z(u.services.links,i=>(l(),d(M,{key:i.label},[i.url?(l(),U(o(E),{key:0,href:i.url,class:q(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":i.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!i.active}]),innerHTML:i.label},null,8,["href","class","innerHTML"])):(l(),d("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:i.label},null,8,es))],64))),128))])])])])])):p("",!0)])]),B(je,{show:x.value,service:a.value,"device-types":u.deviceTypes,onClose:t[4]||(t[4]=i=>x.value=!1),onSaved:J},null,8,["show","service","device-types"]),B(ot,{show:w.value,"device-types":u.deviceTypes,onClose:t[5]||(t[5]=i=>w.value=!1),onSaved:K},null,8,["show","device-types"]),B(re,{show:r.value,processing:h.value,title:"Deactivate Service",message:`Are you sure you want to deactivate ${_.value?.name}? It will no longer be available for new repair orders, but existing orders will be preserved.`,"confirm-text":"Deactivate","cancel-text":"Cancel",type:"danger",onConfirm:W,onCancel:X},null,8,["show","processing","message"])]),_:1})],64))}};export{ls as default};
