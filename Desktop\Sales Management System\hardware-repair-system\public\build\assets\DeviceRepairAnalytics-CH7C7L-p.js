import{r as C,m as g,g as l,o as d,a as n,d as h,h as j,w as m,b as e,t as i,i as D,F as u,y as v,l as R,j as M,I as B}from"./app-BGh5SFxi.js";import{_ as F}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as f}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const L={class:"flex justify-between items-center"},S={class:"flex items-center space-x-4"},T={class:"flex items-center space-x-4"},A=["value"],N={class:"py-12"},V={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},z={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},O={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},U={class:"flex items-center"},I={class:"ml-4"},P={class:"text-2xl font-semibold text-white"},q={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},$={class:"flex items-center"},E={class:"ml-4"},Y={class:"text-2xl font-semibold text-white"},G={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},H={class:"flex items-center"},J={class:"ml-4"},K={class:"text-2xl font-semibold text-white"},Q={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},W={class:"flex items-center"},X={class:"ml-4"},Z={class:"text-2xl font-semibold text-white"},ee={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},te={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},se={class:"p-6"},oe={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ie={class:"p-6"},re={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ae={class:"p-6"},le={class:"overflow-x-auto"},de={class:"min-w-full divide-y divide-gray-700"},ne={class:"bg-gray-800 divide-y divide-gray-700"},ce={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm font-medium text-white"},me={class:"px-6 py-4 whitespace-nowrap"},ue={class:"text-sm text-gray-300"},xe={class:"px-6 py-4 whitespace-nowrap"},ge={class:"text-sm font-medium text-blue-400"},he={class:"px-6 py-4 whitespace-nowrap"},ve={class:"text-sm text-gray-300"},fe={class:"px-6 py-4 whitespace-nowrap"},ye={class:"text-sm text-gray-300"},we={key:0},Re={__name:"DeviceRepairAnalytics",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(r){const a=r,x=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),c=s=>parseFloat(s||0).toLocaleString("en-US"),y=s=>`${parseFloat(s||0).toFixed(1)}%`,w=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],p=C(a.period),_=()=>{window.location.href=route("reports.device-repair-analytics",{period:p.value})},b=g(()=>({type:"line",data:{labels:a.charts.repair_trend?.map(s=>new Date(s.date).toLocaleDateString())||[],datasets:[{label:"Repairs Completed",data:a.charts.repair_trend?.map(s=>s.count)||[],borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!0}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),k=g(()=>({type:"doughnut",data:{labels:a.charts.device_types?.map(s=>s.device_type||"Unknown")||[],datasets:[{data:a.charts.device_types?.map(s=>s.count)||[],backgroundColor:["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4","#84cc16","#f97316"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}}));return(s,t)=>(d(),l(u,null,[n(h(j),{title:"Device Repair Analytics"}),n(F,null,{header:m(()=>[e("div",L,[e("div",S,[n(h(R),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:m(()=>t[1]||(t[1]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),t[2]||(t[2]=e("h2",{class:"font-semibold text-xl text-white leading-tight"}," Device Repair Analytics ",-1))]),e("div",T,[M(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>p.value=o),onChange:_,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(d(),l(u,null,v(w,o=>e("option",{key:o.value,value:o.value},i(o.label),9,A)),64))],544),[[B,p.value]])])])]),default:m(()=>[e("div",N,[e("div",V,[e("div",z,[e("div",O,[e("div",U,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])])],-1)),e("div",I,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Repairs",-1)),e("p",P,i(c(r.metrics.total_repairs)),1)])])]),e("div",q,[e("div",$,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",E,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-400"},"Completion Rate",-1)),e("p",Y,i(y(r.metrics.completion_rate)),1)])])]),e("div",G,[e("div",H,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",J,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-400"},"Avg. Repair Time",-1)),e("p",K,i(c(r.metrics.average_repair_time))+" days",1)])])]),e("div",Q,[e("div",W,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),e("div",X,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-400"},"Revenue per Repair",-1)),e("p",Z,i(x(r.metrics.revenue_per_repair)),1)])])])]),e("div",ee,[e("div",te,[e("div",se,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Repair Trend",-1)),n(f,{config:b.value},null,8,["config"])])]),e("div",oe,[e("div",ie,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Repairs by Device Type",-1)),n(f,{config:k.value},null,8,["config"])])])]),e("div",re,[e("div",ae,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-white mb-6"},"Most Common Issues",-1)),e("div",le,[e("table",de,[t[14]||(t[14]=e("thead",{class:"bg-gray-700"},[e("tr",null,[e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Issue Description "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Device Type "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Frequency "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Repair Time "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Cost ")])],-1)),e("tbody",ne,[(d(!0),l(u,null,v(r.metrics.common_issues,o=>(d(),l("tr",{key:o.id,class:"hover:bg-gray-700"},[e("td",ce,[e("div",pe,i(o.issue_description),1)]),e("td",me,[e("div",ue,i(o.device_type||"N/A"),1)]),e("td",xe,[e("div",ge,i(c(o.frequency)),1)]),e("td",he,[e("div",ve,i(c(o.avg_repair_time))+" days",1)]),e("td",fe,[e("div",ye,i(x(o.avg_cost)),1)])]))),128)),!r.metrics.common_issues||r.metrics.common_issues.length===0?(d(),l("tr",we,t[13]||(t[13]=[e("td",{colspan:"5",class:"px-6 py-4 text-center text-gray-400"}," No repair data available for the selected period. ",-1)]))):D("",!0)])])])])])])])]),_:1})],64))}};export{Re as default};
