import{r as d,m as P,x as G,q as H,g as l,o as r,a as f,d as y,h as Q,w,b as e,t as o,i as u,F as g,y as _,f as V,l as M,c as z,n as Y,j as B,v as J,I as N}from"./app-BGh5SFxi.js";import{_ as K}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as W}from"./DeviceModal-D_VAgM49.js";import{_ as X}from"./ConfirmationModal-Bx9j458V.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const Z={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},ee={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},te={class:"relative flex-1 max-w-md"},se={class:"flex items-center space-x-2"},oe=["value"],re=["value"],le={class:"flex items-center space-x-2"},ae={class:"p-6 space-y-6"},ne={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ie={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},de={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-white"},ce={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ve={class:"flex items-center justify-between"},xe={class:"text-2xl font-bold text-white"},he={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ge={class:"flex items-center justify-between"},pe={class:"text-2xl font-bold text-white"},me={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},fe={class:"flex items-center justify-between"},ye={class:"text-2xl font-bold text-white"},we={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},be={class:"overflow-x-auto"},ke={class:"min-w-full divide-y divide-gray-700"},_e={class:"bg-gray-900 divide-y divide-gray-700"},Me={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"flex items-center"},je={class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-shadow duration-200"},De={class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},He=["d"],Ve={class:"text-sm font-semibold text-white group-hover:text-red-300 transition-colors duration-200"},ze={key:0,class:"text-xs text-gray-400"},Be={class:"px-6 py-4 whitespace-nowrap"},Le={class:"flex items-center"},Te={class:"w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3"},Ae={class:"text-xs font-medium text-white"},Se={class:"text-sm font-medium text-white"},Ne={class:"text-xs text-gray-400"},$e={class:"px-6 py-4 whitespace-nowrap"},Ee={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm"},Ie={class:"px-6 py-4 whitespace-nowrap"},Fe={class:"text-sm text-gray-300"},Oe={key:0,class:"text-xs text-gray-400"},Re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Ue={class:"px-6 py-4 whitespace-nowrap"},qe={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm"},Pe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ge={class:"flex items-center justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},Qe=["onClick"],Ye=["onClick"],Je={key:0},Ke={colspan:"7",class:"px-6 py-12 text-center"},We={class:"flex flex-col items-center"},Xe={class:"text-lg font-medium text-white mb-2"},Ze={class:"text-gray-400 text-sm mb-4"},et={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},tt={class:"flex items-center justify-between"},st={class:"flex-1 flex justify-between sm:hidden"},ot={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},rt={class:"text-sm text-gray-400"},lt={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},at=["innerHTML"],xt={__name:"Index",props:{devices:Object,customers:Array,deviceTypes:Array,filters:Object},setup(a){const c=a,p=d(!1),b=d(!1),C=d(null),v=d(null),m=d(!1),n=d(c.filters?.search||""),x=d(c.filters?.device_type||"all"),h=d(c.filters?.customer||"all"),j=d(c.filters?.sort||"created_at"),D=d(c.filters?.direction||"desc"),L=P(()=>{if(!c.devices?.data)return[];let i=[...c.devices.data];if(n.value){const t=n.value.toLowerCase();i=i.filter(s=>s.brand?.toLowerCase().includes(t)||s.model?.toLowerCase().includes(t)||s.serial_number?.toLowerCase().includes(t)||s.customer?.full_name?.toLowerCase().includes(t))}return i});let T;G(n,i=>{clearTimeout(T),T=setTimeout(()=>{k()},300)});const A=()=>{C.value=null,p.value=!0},$=i=>{C.value=i,p.value=!0},E=i=>{v.value=i,b.value=!0},I=()=>{p.value=!1,H.reload({only:["devices"]})},F=()=>{v.value&&(m.value=!0,H.delete(route("devices.destroy",v.value.id),{onSuccess:()=>{b.value=!1,v.value=null,m.value=!1},onError:()=>{m.value=!1}}))},O=()=>{b.value=!1,v.value=null,m.value=!1},k=()=>{const i={search:n.value||void 0,device_type:x.value!=="all"?x.value:void 0,customer:h.value!=="all"?h.value:void 0,sort:j.value!=="created_at"?j.value:void 0,direction:D.value!=="desc"?D.value:void 0};H.get(route("devices.index"),i,{preserveState:!0,replace:!0})},R=()=>{console.log("Exporting devices...")},S=()=>{n.value="",x.value="all",h.value="all",j.value="created_at",D.value="desc",k()},U=i=>{const t={Smartphone:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z",Laptop:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",Desktop:"M9 17v-2m3 2v-4m3 4v-6m2 10H4a2 2 0 01-2-2V5a2 2 0 012-2h16a2 2 0 012 2v14a2 2 0 01-2 2z",Printer:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z",Tablet:"M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-7.172a2 2 0 00-1.414.586L3 12z"};return t[i]||t.Smartphone};return(i,t)=>(r(),l(g,null,[f(y(Q),{title:"Devices"}),f(K,null,{header:w(()=>[e("div",Z,[t[11]||(t[11]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Device Management "),e("p",{class:"text-gray-400 text-sm"},"Track and manage customer devices")],-1)),e("div",ee,[e("div",te,[B(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>n.value=s),type:"text",placeholder:"Search devices...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[J,n.value]]),t[6]||(t[6]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),n.value?(r(),l("button",{key:0,onClick:t[1]||(t[1]=s=>n.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):u("",!0)]),e("div",se,[B(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>x.value=s),onChange:k,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[t[7]||(t[7]=e("option",{value:"all"},"All Types",-1)),(r(!0),l(g,null,_(a.deviceTypes,s=>(r(),l("option",{key:s.id,value:s.id},o(s.name),9,oe))),128))],544),[[N,x.value]]),B(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>h.value=s),onChange:k,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[t[8]||(t[8]=e("option",{value:"all",class:"bg-gray-800 text-white"}," All Customers ",-1)),(r(!0),l(g,null,_(a.customers,s=>(r(),l("option",{key:s.id,value:s.id,class:"bg-gray-800 text-white"},o(s.full_name),9,re))),128))],544),[[N,h.value]])]),e("div",le,[e("button",{onClick:R,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[9]||(t[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("span",null,"Export",-1)])),n.value||x.value!=="all"||h.value!=="all"?(r(),l("button",{key:0,onClick:S,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):u("",!0),e("button",{onClick:A,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Device",-1)]))])])])]),default:w(()=>[e("div",ae,[e("div",ne,[e("div",ie,[e("div",de,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm text-gray-400"},"Total Devices",-1)),e("p",ue,o(a.devices.total||0),1)]),t[13]||(t[13]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1))])]),e("div",ce,[e("div",ve,[e("div",null,[t[14]||(t[14]=e("p",{class:"text-sm text-gray-400"},"Smartphones",-1)),e("p",xe,o(Math.floor((a.devices.total||0)*.6)),1)]),t[15]||(t[15]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1))])]),e("div",he,[e("div",ge,[e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm text-gray-400"},"Laptops",-1)),e("p",pe,o(Math.floor((a.devices.total||0)*.3)),1)]),t[17]||(t[17]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1))])]),e("div",me,[e("div",fe,[e("div",null,[t[18]||(t[18]=e("p",{class:"text-sm text-gray-400"},"Other Devices",-1)),e("p",ye,o(Math.floor((a.devices.total||0)*.1)),1)]),t[19]||(t[19]=e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})])],-1))])])]),e("div",we,[t[29]||(t[29]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Device Registry")]),e("div",{class:"flex items-center space-x-2"},[e("button",{class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"})])]),e("button",{class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])])])])],-1)),e("div",be,[e("table",ke,[t[26]||(t[26]=e("thead",{class:"bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Device"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Customer"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Type"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Serial/IMEI"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Year"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Repairs"),e("th",{class:"px-6 py-4 text-right text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",_e,[(r(!0),l(g,null,_(L.value,s=>(r(),l("tr",{key:s.id,class:"hover:bg-gray-800 transition-colors duration-200 group"},[e("td",Me,[e("div",Ce,[e("div",je,[(r(),l("svg",De,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:U(s.device_type?.name)},null,8,He)]))]),e("div",null,[e("div",Ve,o(s.brand)+" "+o(s.model),1),s.color?(r(),l("div",ze,o(s.color),1)):u("",!0)])])]),e("td",Be,[e("div",Le,[e("div",Te,[e("span",Ae,o(s.customer?.first_name?.charAt(0))+o(s.customer?.last_name?.charAt(0)),1)]),e("div",null,[e("div",Se,o(s.customer?.full_name),1),e("div",Ne,o(s.customer?.phone),1)])])]),e("td",$e,[e("span",Ee,o(s.device_type?.name),1)]),e("td",Ie,[e("div",Fe,o(s.serial_number||"N/A"),1),s.imei?(r(),l("div",Oe,"IMEI: "+o(s.imei),1)):u("",!0)]),e("td",Re,o(s.year||"N/A"),1),e("td",Ue,[e("span",qe,[t[20]||(t[20]=e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),V(" "+o(s.repair_orders_count||0),1)])]),e("td",Pe,[e("div",Ge,[f(y(M),{href:i.route("devices.show",s.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Device"},{default:w(()=>t[21]||(t[21]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[21]},1032,["href"]),e("button",{onClick:q=>$(s),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Device"},t[22]||(t[22]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Qe),e("button",{onClick:q=>E(s),class:"p-2 text-red-400 hover:text-red-300 hover:bg-red-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Delete Device"},t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ye)])])]))),128)),L.value.length===0?(r(),l("tr",Je,[e("td",Ke,[e("div",We,[t[25]||(t[25]=e("div",{class:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4"},[e("svg",{class:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1)),e("h3",Xe,o(n.value?"No devices found":"No devices registered"),1),e("p",Ze,o(n.value?`No devices match "${n.value}"`:"Get started by registering your first device"),1),n.value?(r(),l("button",{key:1,onClick:S,class:"text-red-400 hover:text-red-300 text-sm font-medium"}," Clear search and show all devices → ")):(r(),l("button",{key:0,onClick:A,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Register your first device",-1)])))])])])):u("",!0)])])]),a.devices.links?(r(),l("div",et,[e("div",tt,[e("div",st,[a.devices.prev_page_url?(r(),z(y(M),{key:0,href:a.devices.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:w(()=>t[27]||(t[27]=[V(" Previous ",-1)])),_:1,__:[27]},8,["href"])):u("",!0),a.devices.next_page_url?(r(),z(y(M),{key:1,href:a.devices.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:w(()=>t[28]||(t[28]=[V(" Next ",-1)])),_:1,__:[28]},8,["href"])):u("",!0)]),e("div",ot,[e("div",null,[e("p",rt," Showing "+o(a.devices.from)+" to "+o(a.devices.to)+" of "+o(a.devices.total)+" results ",1)]),e("div",null,[e("nav",lt,[(r(!0),l(g,null,_(a.devices.links,s=>(r(),l(g,{key:s.label},[s.url?(r(),z(y(M),{key:0,href:s.url,class:Y(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":s.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!s.active}]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(r(),l("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:s.label},null,8,at))],64))),128))])])])])])):u("",!0)])]),f(W,{show:p.value,device:C.value,customers:a.customers,"device-types":a.deviceTypes,onClose:t[4]||(t[4]=s=>p.value=!1),onSaved:I},null,8,["show","device","customers","device-types"]),f(X,{show:b.value,processing:m.value,title:"Delete Device",message:`Are you sure you want to delete ${v.value?.brand} ${v.value?.model}? This action cannot be undone and will also delete all associated repair orders.`,"confirm-text":"Delete Device","cancel-text":"Cancel",type:"danger",onConfirm:F,onCancel:O},null,8,["show","processing","message"])]),_:1})],64))}};export{xt as default};
