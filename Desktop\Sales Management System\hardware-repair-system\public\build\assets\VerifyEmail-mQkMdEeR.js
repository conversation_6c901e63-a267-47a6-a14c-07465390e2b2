import{u as c,m as g,c as p,o as n,w as i,a as o,b as a,g as y,i as _,d as t,h as v,e as b,n as h,f as r,l as k}from"./app-BGh5SFxi.js";import{_ as x}from"./GuestLayout-_pC6MrRa.js";import{P as w}from"./PrimaryButton-Cv9hkgaW.js";import"./ApplicationLogo-CxXkoeMW.js";const V={key:0,class:"mb-4 text-sm font-medium text-green-600"},B={class:"mt-4 flex items-center justify-between"},j={__name:"VerifyEmail",props:{status:{type:String}},setup(d){const l=d,s=c({}),u=()=>{s.post(route("verification.send"))},m=g(()=>l.status==="verification-link-sent");return(f,e)=>(n(),p(x,null,{default:i(()=>[o(t(v),{title:"Email Verification"}),e[2]||(e[2]=a("div",{class:"mb-4 text-sm text-gray-600"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),m.value?(n(),y("div",V," A new verification link has been sent to the email address you provided during registration. ")):_("",!0),a("form",{onSubmit:b(u,["prevent"])},[a("div",B,[o(w,{class:h({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:i(()=>e[0]||(e[0]=[r(" Resend Verification Email ",-1)])),_:1,__:[0]},8,["class","disabled"]),o(t(k),{href:f.route("logout"),method:"post",as:"button",class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},{default:i(()=>e[1]||(e[1]=[r("Log Out",-1)])),_:1,__:[1]},8,["href"])])],32)]),_:1,__:[2]}))}};export{j as default};
