import{r as j,m as g,g as i,o as d,a as n,d as h,h as B,w as m,b as t,t as r,i as D,F as p,y as f,l as L,j as N,I as S}from"./app-BGh5SFxi.js";import{_ as F}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as y}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const A={class:"flex justify-between items-center"},M={class:"flex items-center space-x-4"},O={class:"flex items-center space-x-4"},P=["value"],V={class:"py-12"},z={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},R={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},T={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},U={class:"flex items-center"},W={class:"ml-4"},G={class:"text-2xl font-semibold text-white"},$={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},E={class:"flex items-center"},H={class:"ml-4"},I={class:"text-2xl font-semibold text-white"},Y={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},q={class:"flex items-center"},J={class:"ml-4"},K={class:"text-2xl font-semibold text-white"},Q={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},X={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},Z={class:"p-6"},tt={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},et={class:"p-6"},st={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ot={class:"p-6"},rt={class:"overflow-x-auto"},lt={class:"min-w-full divide-y divide-gray-700"},at={class:"bg-gray-800 divide-y divide-gray-700"},it={class:"px-6 py-4 whitespace-nowrap"},dt={class:"text-sm font-medium text-white"},nt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm text-gray-300"},ut={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-300"},pt={class:"px-6 py-4 whitespace-nowrap"},xt={class:"text-sm font-medium text-green-400"},gt={class:"px-6 py-4 whitespace-nowrap"},ht={class:"text-sm text-gray-300"},ft={key:0},Ct={__name:"CustomerAnalytics",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(l){const a=l,x=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),c=s=>parseFloat(s||0).toLocaleString("en-US"),v=s=>`${parseFloat(s||0).toFixed(1)}%`,w=s=>s?s.startsWith("+63")?s:s.startsWith("63")?"+"+s:s.startsWith("09")?"+63"+s.substring(1):s:"N/A",_=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],u=j(a.period),b=()=>{window.location.href=route("reports.customer-analytics",{period:u.value})},k=g(()=>({type:"line",data:{labels:a.charts.customer_growth?.map(s=>new Date(s.date).toLocaleDateString())||[],datasets:[{label:"New Customers",data:a.charts.customer_growth?.map(s=>s.count)||[],borderColor:"rgb(147, 51, 234)",backgroundColor:"rgba(147, 51, 234, 0.1)",tension:.4,fill:!0}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),C=g(()=>({type:"doughnut",data:{labels:a.charts.customers_by_city?.map(s=>s.city||"Unknown")||[],datasets:[{data:a.charts.customers_by_city?.map(s=>s.count)||[],backgroundColor:["#8b5cf6","#06b6d4","#10b981","#f59e0b","#ef4444","#6366f1","#ec4899","#84cc16","#f97316","#64748b"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}}));return(s,e)=>(d(),i(p,null,[n(h(B),{title:"Customer Analytics"}),n(F,null,{header:m(()=>[t("div",A,[t("div",M,[n(h(L),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:m(()=>e[1]||(e[1]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),e[2]||(e[2]=t("h2",{class:"font-semibold text-xl text-white leading-tight"}," Customer Analytics ",-1))]),t("div",O,[N(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>u.value=o),onChange:b,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(d(),i(p,null,f(_,o=>t("option",{key:o.value,value:o.value},r(o.label),9,P)),64))],544),[[S,u.value]])])])]),default:m(()=>[t("div",V,[t("div",z,[t("div",R,[t("div",T,[t("div",U,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])])],-1)),t("div",W,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"New Customers",-1)),t("p",G,r(c(l.metrics.new_customers)),1)])])]),t("div",$,[t("div",E,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",H,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-400"},"Customer Retention",-1)),t("p",I,r(v(l.metrics.customer_retention)),1)])])]),t("div",Y,[t("div",q,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})])])],-1)),t("div",J,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-400"},"Active Customers",-1)),t("p",K,r(c(l.metrics.top_customers?.length||0)),1)])])])]),t("div",Q,[t("div",X,[t("div",Z,[e[9]||(e[9]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Customer Growth Trend",-1)),n(y,{config:k.value},null,8,["config"])])]),t("div",tt,[t("div",et,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-white mb-4"},"Customers by City",-1)),n(y,{config:C.value},null,8,["config"])])])]),t("div",st,[t("div",ot,[e[13]||(e[13]=t("h3",{class:"text-lg font-medium text-white mb-6"},"Top Customers by Revenue",-1)),t("div",rt,[t("table",lt,[e[12]||(e[12]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Customer "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Phone "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Total Orders "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Total Revenue "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Order Value ")])],-1)),t("tbody",at,[(d(!0),i(p,null,f(l.metrics.top_customers,o=>(d(),i("tr",{key:o.id,class:"hover:bg-gray-700"},[t("td",it,[t("div",dt,r(o.name),1)]),t("td",nt,[t("div",ct,r(w(o.phone)),1)]),t("td",ut,[t("div",mt,r(c(o.total_orders)),1)]),t("td",pt,[t("div",xt,r(x(o.total_spent)),1)]),t("td",gt,[t("div",ht,r(x(o.total_spent/o.total_orders)),1)])]))),128)),!l.metrics.top_customers||l.metrics.top_customers.length===0?(d(),i("tr",ft,e[11]||(e[11]=[t("td",{colspan:"5",class:"px-6 py-4 text-center text-gray-400"}," No customer data available for the selected period. ",-1)]))):D("",!0)])])])])])])])]),_:1})],64))}};export{Ct as default};
