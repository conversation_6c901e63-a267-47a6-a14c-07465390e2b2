import{_ as E}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as L}from"./CustomerModal-D_puuOms.js";import{_ as O}from"./DeviceModal-D_VAgM49.js";import{_ as T}from"./RepairOrderModal-DZH8SLto.js";import{_ as F}from"./ConfirmationModal-Bx9j458V.js";import{r as c,m as M,g as l,o as n,a,d as u,h as I,w as d,b as e,i as k,t as o,f,l as b,F as _,y as D,n as P,q as m}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const q={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},Q={class:"flex items-center space-x-4"},U={class:"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg"},G={class:"text-lg font-bold text-white"},J={class:"text-2xl font-bold text-white"},K={class:"text-gray-400 text-sm"},W={class:"p-6 space-y-8"},X={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Y={class:"lg:col-span-2"},Z={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},te={class:"flex items-center space-x-2"},se={class:"text-white truncate"},oe=["href"],re={class:"flex items-center space-x-2"},le={class:"text-white"},ne=["href"],ae={key:0,class:"md:col-span-2"},ie={class:"text-white"},de={key:1,class:"md:col-span-2"},ce={class:"text-gray-300"},ue={class:"space-y-6"},me={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ge={class:"space-y-4"},xe={class:"flex items-center justify-between"},he={class:"text-2xl font-bold text-white"},ve={class:"flex items-center justify-between"},fe={class:"text-2xl font-bold text-white"},be={class:"flex items-center justify-between"},pe={class:"text-sm text-white"},we={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},ye={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},ke={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},_e={class:"flex items-center justify-between"},Ce={class:"p-6"},Me={key:0,class:"space-y-4"},De={class:"flex items-center justify-between"},je={class:"font-medium text-white"},Se={class:"text-sm text-gray-400"},Ve={key:0,class:"text-xs text-gray-500"},Be={key:1,class:"text-center py-8"},$e={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},ze={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},He={class:"flex items-center justify-between"},Ae={class:"p-6"},Ne={key:0,class:"space-y-4"},Re={class:"flex items-center justify-between mb-2"},Ee={class:"text-sm font-medium text-white"},Le={class:"text-sm text-gray-400 mb-2"},Oe={class:"text-xs text-gray-500"},Te={key:1,class:"text-center py-8"},Ke={__name:"Show",props:{customer:Object},setup(s){const p=s,g=c(!1),w=c(!1),x=c(!1),h=c(!1),v=c(!1),C=M(()=>p.customer.repair_orders?.slice(0,5)||[]),y=M(()=>p.customer.devices||[]),j=()=>{g.value=!0},S=()=>{x.value=!0},V=()=>{h.value=!0},B=()=>{w.value=!0},$=()=>{g.value=!1,m.reload({only:["customer"]})},z=()=>{v.value=!0,m.delete(route("customers.destroy",p.customer.id),{onSuccess:()=>{m.visit(route("customers.index"))},onError:()=>{v.value=!1}})},H=()=>{w.value=!1,v.value=!1},A=()=>{x.value=!1,m.reload({only:["customer"]})},N=()=>{h.value=!1,m.reload({only:["customer"]})},R=i=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[i]||t.pending};return(i,t)=>(n(),l(_,null,[a(u(I),{title:`${s.customer.full_name} - Customer Details`},null,8,["title"]),a(E,null,{header:d(()=>[e("div",q,[e("div",Q,[a(u(b),{href:i.route("customers.index"),class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},{default:d(()=>t[3]||(t[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[3]},8,["href"]),e("div",U,[e("span",G,o(s.customer.first_name?.charAt(0))+o(s.customer.last_name?.charAt(0)),1)]),e("div",null,[e("h2",J,o(s.customer.full_name),1),e("p",K,"Customer ID: #"+o(String(s.customer.id).padStart(4,"0")),1)])]),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:j,class:"bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[4]||(t[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),e("span",null,"Edit",-1)])),e("button",{onClick:B,class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),e("span",null,"Delete",-1)]))])])]),default:d(()=>[e("div",W,[e("div",X,[e("div",Y,[e("div",Z,[t[13]||(t[13]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Contact Information")],-1)),e("div",ee,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Facebook Profile",-1)),e("div",te,[e("p",se,o(s.customer.facebook_link),1),e("a",{href:s.customer.facebook_link,target:"_blank",class:"text-blue-400 hover:text-blue-300 flex-shrink-0"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})],-1)]),8,oe)])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Phone Number",-1)),e("div",re,[e("p",le,o(s.customer.phone),1),e("a",{href:`tel:${s.customer.phone}`,class:"text-red-400 hover:text-red-300"},t[8]||(t[8]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)]),8,ne)])]),s.customer.address?(n(),l("div",ae,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Address",-1)),e("p",ie,[f(o(s.customer.address),1),t[10]||(t[10]=e("br",null,null,-1)),f(" "+o(s.customer.city)+", "+o(s.customer.state)+" "+o(s.customer.zip_code),1)])])):k("",!0),s.customer.notes?(n(),l("div",de,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Notes",-1)),e("p",ce,o(s.customer.notes),1)])):k("",!0)])])]),e("div",ue,[e("div",me,[t[17]||(t[17]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Statistics",-1)),e("div",ge,[e("div",xe,[t[14]||(t[14]=e("span",{class:"text-gray-400"},"Total Devices",-1)),e("span",he,o(y.value.length),1)]),e("div",ve,[t[15]||(t[15]=e("span",{class:"text-gray-400"},"Total Repairs",-1)),e("span",fe,o(s.customer.repair_orders?.length||0),1)]),e("div",be,[t[16]||(t[16]=e("span",{class:"text-gray-400"},"Customer Since",-1)),e("span",pe,o(new Date(s.customer.created_at).toLocaleDateString()),1)])])]),e("div",{class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},[t[20]||(t[20]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Quick Actions",-1)),e("div",{class:"space-y-3"},[e("button",{onClick:S,class:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[18]||(t[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Device",-1)])),e("button",{onClick:V,class:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[19]||(t[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("span",null,"Create Repair Order",-1)]))])])])]),e("div",we,[e("div",ye,[e("div",ke,[e("div",_e,[t[22]||(t[22]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Registered Devices")],-1)),a(u(b),{href:i.route("devices.create",{customer_id:s.customer.id}),class:"text-red-400 hover:text-red-300 text-sm font-medium"},{default:d(()=>t[21]||(t[21]=[f(" Add Device → ",-1)])),_:1,__:[21]},8,["href"])])]),e("div",Ce,[y.value.length>0?(n(),l("div",Me,[(n(!0),l(_,null,D(y.value,r=>(n(),l("div",{key:r.id,class:"bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-colors duration-200"},[e("div",De,[e("div",null,[e("h4",je,o(r.brand)+" "+o(r.model),1),e("p",Se,o(r.device_type?.name),1),r.serial_number?(n(),l("p",Ve,"S/N: "+o(r.serial_number),1)):k("",!0)]),a(u(b),{href:i.route("devices.show",r.id),class:"text-red-400 hover:text-red-300"},{default:d(()=>t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:2,__:[23]},1032,["href"])])]))),128))])):(n(),l("div",Be,t[24]||(t[24]=[e("svg",{class:"w-12 h-12 text-gray-600 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-400 text-sm"},"No devices registered",-1)])))])]),e("div",$e,[e("div",ze,[e("div",He,[t[26]||(t[26]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Recent Repairs")],-1)),a(u(b),{href:i.route("repair-orders.index",{customer:s.customer.id}),class:"text-red-400 hover:text-red-300 text-sm font-medium"},{default:d(()=>t[25]||(t[25]=[f(" View All → ",-1)])),_:1,__:[25]},8,["href"])])]),e("div",Ae,[C.value.length>0?(n(),l("div",Ne,[(n(!0),l(_,null,D(C.value,r=>(n(),l("div",{key:r.id,class:"bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-colors duration-200"},[e("div",Re,[e("span",Ee,o(r.order_number),1),e("span",{class:P(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",R(r.status)])},o(r.status.replace("_"," ").toUpperCase()),3)]),e("p",Le,o(r.service?.name),1),e("p",Oe,o(new Date(r.created_at).toLocaleDateString()),1)]))),128))])):(n(),l("div",Te,t[27]||(t[27]=[e("svg",{class:"w-12 h-12 text-gray-600 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"text-gray-400 text-sm"},"No repair orders yet",-1)])))])])])]),a(L,{show:g.value,customer:s.customer,onClose:t[0]||(t[0]=r=>g.value=!1),onSaved:$},null,8,["show","customer"]),a(F,{show:w.value,processing:v.value,title:"Delete Customer",message:`Are you sure you want to delete ${s.customer.full_name}? This action cannot be undone and will also delete all associated devices and repair orders.`,"confirm-text":"Delete Customer","cancel-text":"Cancel",type:"danger",onConfirm:z,onCancel:H},null,8,["show","processing","message"]),a(O,{show:x.value,customers:[s.customer],"device-types":i.$page.props.deviceTypes||[],"preselected-customer":s.customer.id,onClose:t[1]||(t[1]=r=>x.value=!1),onSaved:A},null,8,["show","customers","device-types","preselected-customer"]),a(T,{show:h.value,customers:[s.customer],devices:s.customer.devices||[],services:i.$page.props.services||[],technicians:i.$page.props.technicians||[],"preselected-customer":s.customer.id,onClose:t[2]||(t[2]=r=>h.value=!1),onSaved:N},null,8,["show","customers","devices","services","technicians","preselected-customer"])]),_:1})],64))}};export{Ke as default};
