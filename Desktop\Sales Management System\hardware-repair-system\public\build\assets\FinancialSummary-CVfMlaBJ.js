import{_ as j}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as F,m as _,g as d,o as c,a as b,d as w,h as M,w as m,b as t,t as s,n as y,F as x,y as p,J as S,l as C,f as D}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const B={class:"flex items-center justify-between"},L={class:"text-sm text-gray-400 mt-1"},P={class:"flex space-x-3"},R={class:"py-12"},O={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8"},V={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},T={class:"bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl"},z={class:"flex items-center justify-between"},G={class:"text-3xl font-bold text-white"},E={class:"bg-gradient-to-br from-red-900 to-red-800 border border-red-700 rounded-xl p-6 shadow-xl"},H={class:"flex items-center justify-between"},I={class:"text-3xl font-bold text-white"},U={class:"bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl"},A={class:"flex items-center justify-between"},N={class:"text-3xl font-bold text-white"},$={class:"bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl"},q={class:"flex items-center justify-between"},J={class:"text-3xl font-bold text-white"},K={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Q={class:"grid grid-cols-1 lg:grid-cols-4 gap-6"},W={class:"lg:col-span-1"},X={class:"text-center"},Y={class:"relative w-32 h-32 mx-auto mb-4"},Z={class:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36"},tt=["stroke-dasharray"],et={class:"absolute inset-0 flex items-center justify-center"},st={class:"text-center"},ot={class:"text-2xl font-bold text-white"},lt={class:"lg:col-span-3 space-y-4"},nt={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},rt={class:"text-green-400 font-semibold"},at={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},it={class:"text-blue-400 font-semibold"},dt={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},ct={class:"text-yellow-400 font-semibold"},gt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ut={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},xt={class:"space-y-4"},ft={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},bt={class:"text-white font-semibold"},mt={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},pt={class:"text-white font-semibold"},ht={class:"pt-4 border-t border-gray-600"},vt={class:"flex justify-between items-center"},_t={class:"text-white font-bold text-lg"},wt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},yt={class:"space-y-4"},kt={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},jt={class:"text-red-400 font-semibold"},Ft={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},Mt={class:"text-green-400 font-semibold"},St={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},Ct={class:"text-green-400 font-semibold"},Dt={class:"pt-4 border-t border-gray-600"},Bt={class:"flex justify-between items-center"},Lt={class:"text-green-400 font-bold text-lg"},Pt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Rt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Ot={class:"space-y-3"},Vt={class:"text-white font-medium"},Tt={class:"text-gray-400 text-sm"},zt={class:"text-green-400 font-semibold"},Gt={class:"text-center p-6 bg-gray-700 rounded-lg"},Et={class:"text-3xl font-bold text-yellow-400"},Ht={class:"space-y-2"},It={class:"text-gray-400 text-sm"},Ut={class:"text-green-400 font-medium text-sm"},At={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Nt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},$t={class:"flex justify-between items-start mb-2"},qt={class:"text-white font-medium"},Jt={class:"text-sm text-gray-400"},Kt={class:"text-2xl font-bold text-red-400"},Qt={class:"mt-2 bg-gray-600 rounded-full h-2"},Wt={class:"mt-6 pt-4 border-t border-gray-700"},Xt={class:"flex justify-between items-center"},Yt={class:"text-red-400 font-bold text-xl"},Zt={__name:"FinancialSummary",props:{financialData:{type:Object,default:()=>({total_revenue:0,total_costs:0,gross_profit:0,gross_margin:0})},profitLoss:{type:Object,default:()=>({revenue:{labor:0,parts:0,total:0},costs:{parts:0,total:0},profit:{gross:0,labor_profit:0,parts_profit:0}})},cashFlow:{type:Object,default:()=>({cash_inflows:[],outstanding_receivables:0,daily_cash_flow:[]})},expenseBreakdown:{type:Object,default:()=>({parts_expenses:[],total_parts_cost:0})},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({start:new Date().toISOString(),end:new Date().toISOString()})}},setup(o){const g=o,n=a=>"₱"+parseFloat(a||0).toLocaleString("en-US",{minimumFractionDigits:2}),h=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),f=a=>`${a.toFixed(1)}%`,i=_(()=>{const a=g.financialData?.gross_margin||0,e=g.profitLoss?.profit?.gross||0,l=g.profitLoss?.revenue?.total||0,u=e>0&&l>0?e/l*100:0;let r=0;a>=50?r+=40:a>=30?r+=30:a>=20&&(r+=20),u>=20?r+=30:u>=15?r+=25:u>=10&&(r+=20);const v=g.cashFlow?.outstanding_receivables||0;return v<l*.1?r+=30:v<l*.2?r+=20:r+=10,{score:Math.min(100,r),status:r>=80?"Excellent":r>=60?"Good":r>=40?"Fair":"Poor",grossMargin:a,profitMargin:u}}),k=_(()=>g.cashFlow?.daily_cash_flow?.slice(-7)||[]);return(a,e)=>(c(),d(x,null,[b(w(M),{title:"Financial Summary"}),b(j,null,{header:m(()=>[t("div",B,[t("div",null,[e[0]||(e[0]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Financial Summary ",-1)),t("p",L,s(h(o.dateRange.start))+" - "+s(h(o.dateRange.end)),1)]),t("div",P,[b(w(C),{href:a.route("reports.index"),class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"},{default:m(()=>e[1]||(e[1]=[D(" Back to Dashboard ",-1)])),_:1,__:[1]},8,["href"])])])]),default:m(()=>[t("div",R,[t("div",O,[t("div",V,[t("div",T,[t("div",z,[t("div",null,[e[2]||(e[2]=t("p",{class:"text-sm text-green-200"},"Total Revenue",-1)),t("p",G,s(n(o.financialData.total_revenue)),1)]),e[3]||(e[3]=t("div",{class:"p-3 bg-green-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-green-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),t("div",E,[t("div",H,[t("div",null,[e[4]||(e[4]=t("p",{class:"text-sm text-red-200"},"Total Costs",-1)),t("p",I,s(n(o.financialData.total_costs)),1)]),e[5]||(e[5]=t("div",{class:"p-3 bg-red-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-red-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"})])],-1))])]),t("div",U,[t("div",A,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm text-blue-200"},"Gross Profit",-1)),t("p",N,s(n(o.financialData.gross_profit)),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-blue-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-blue-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),t("div",$,[t("div",q,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm text-purple-200"},"Gross Margin",-1)),t("p",J,s(f(o.financialData.gross_margin)),1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-purple-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})])],-1))])])]),t("div",K,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Financial Health Score",-1)),t("div",Q,[t("div",W,[t("div",X,[t("div",Y,[(c(),d("svg",Z,[e[10]||(e[10]=t("path",{class:"text-gray-600",stroke:"currentColor","stroke-width":"3",fill:"none",d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"},null,-1)),t("path",{class:y(i.value.score>=80?"text-green-400":i.value.score>=60?"text-yellow-400":"text-red-400"),stroke:"currentColor","stroke-width":"3",fill:"none","stroke-linecap":"round","stroke-dasharray":`${i.value.score}, 100`,d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"},null,10,tt)])),t("div",et,[t("div",st,[t("div",ot,s(i.value.score.toFixed(0)),1),e[11]||(e[11]=t("div",{class:"text-xs text-gray-400"},"Score",-1))])])]),t("p",{class:y(["text-lg font-semibold",i.value.score>=80?"text-green-400":i.value.score>=60?"text-yellow-400":"text-red-400"])},s(i.value.status),3)])]),t("div",lt,[t("div",nt,[e[12]||(e[12]=t("span",{class:"text-white"},"Gross Margin",-1)),t("span",rt,s(f(i.value.grossMargin)),1)]),t("div",at,[e[13]||(e[13]=t("span",{class:"text-white"},"Profit Margin",-1)),t("span",it,s(f(i.value.profitMargin)),1)]),t("div",dt,[e[14]||(e[14]=t("span",{class:"text-white"},"Outstanding Receivables",-1)),t("span",ct,s(n(o.cashFlow.outstanding_receivables)),1)])])])]),t("div",gt,[t("div",ut,[e[19]||(e[19]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Revenue Breakdown",-1)),t("div",xt,[t("div",ft,[e[16]||(e[16]=t("div",{class:"flex items-center"},[t("div",{class:"w-4 h-4 bg-blue-500 rounded mr-3"}),t("span",{class:"text-white"},"Labor Revenue")],-1)),t("span",bt,s(n(o.profitLoss.revenue.labor)),1)]),t("div",mt,[e[17]||(e[17]=t("div",{class:"flex items-center"},[t("div",{class:"w-4 h-4 bg-green-500 rounded mr-3"}),t("span",{class:"text-white"},"Parts Revenue")],-1)),t("span",pt,s(n(o.profitLoss.revenue.parts)),1)]),t("div",ht,[t("div",vt,[e[18]||(e[18]=t("span",{class:"text-gray-400"},"Total Revenue",-1)),t("span",_t,s(n(o.profitLoss.revenue.total)),1)])])])]),t("div",wt,[e[24]||(e[24]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Cost & Profit Analysis",-1)),t("div",yt,[t("div",kt,[e[20]||(e[20]=t("span",{class:"text-white"},"Parts Cost",-1)),t("span",jt,s(n(o.profitLoss.costs.parts)),1)]),t("div",Ft,[e[21]||(e[21]=t("span",{class:"text-white"},"Labor Profit",-1)),t("span",Mt,s(n(o.profitLoss.profit.labor_profit)),1)]),t("div",St,[e[22]||(e[22]=t("span",{class:"text-white"},"Parts Profit",-1)),t("span",Ct,s(n(o.profitLoss.profit.parts_profit)),1)]),t("div",Dt,[t("div",Bt,[e[23]||(e[23]=t("span",{class:"text-gray-400"},"Gross Profit",-1)),t("span",Lt,s(n(o.profitLoss.profit.gross)),1)])])])])]),t("div",Pt,[e[29]||(e[29]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Cash Flow Analysis",-1)),t("div",Rt,[t("div",null,[e[25]||(e[25]=t("h4",{class:"text-md font-semibold text-white mb-4"},"Cash Inflows by Method",-1)),t("div",Ot,[(c(!0),d(x,null,p(o.cashFlow.cash_inflows,l=>(c(),d("div",{key:l.payment_method,class:"flex justify-between items-center p-3 bg-gray-700 rounded-lg"},[t("div",null,[t("p",Vt,s(l.payment_method.replace("_"," ").toUpperCase()),1),t("p",Tt,s(l.transaction_count)+" transactions",1)]),t("span",zt,s(n(l.total_amount)),1)]))),128))])]),t("div",null,[e[27]||(e[27]=t("h4",{class:"text-md font-semibold text-white mb-4"},"Outstanding Receivables",-1)),t("div",Gt,[t("p",Et,s(n(o.cashFlow.outstanding_receivables)),1),e[26]||(e[26]=t("p",{class:"text-gray-400 text-sm mt-2"},"Amount pending collection",-1))])]),t("div",null,[e[28]||(e[28]=t("h4",{class:"text-md font-semibold text-white mb-4"},"Recent Cash Flow (7 Days)",-1)),t("div",Ht,[(c(!0),d(x,null,p(k.value,l=>(c(),d("div",{key:l.date,class:"flex justify-between items-center p-2 bg-gray-700 rounded"},[t("span",It,s(new Date(l.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})),1),t("span",Ut,s(n(l.inflow)),1)]))),128))])])])]),t("div",At,[e[31]||(e[31]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Expense Breakdown by Category",-1)),t("div",Nt,[(c(!0),d(x,null,p(o.expenseBreakdown.parts_expenses,l=>(c(),d("div",{key:l.category,class:"bg-gray-700 rounded-lg p-4"},[t("div",$t,[t("h4",qt,s(l.category),1),t("span",Jt,s(l.total_quantity)+" units",1)]),t("p",Kt,s(n(l.total_cost)),1),t("div",Qt,[t("div",{class:"bg-red-500 h-2 rounded-full",style:S({width:o.expenseBreakdown.total_parts_cost>0?l.total_cost/o.expenseBreakdown.total_parts_cost*100+"%":"0%"})},null,4)])]))),128))]),t("div",Wt,[t("div",Xt,[e[30]||(e[30]=t("span",{class:"text-gray-400"},"Total Parts Expenses",-1)),t("span",Yt,s(n(o.expenseBreakdown.total_parts_cost)),1)])])])])])]),_:1})],64))}},le=F(Zt,[["__scopeId","data-v-13205f37"]]);export{le as default};
