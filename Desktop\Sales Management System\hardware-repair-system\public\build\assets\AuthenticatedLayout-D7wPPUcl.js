import{A as _}from"./ApplicationLogo-CxXkoeMW.js";import{_ as k,a as L}from"./DropdownLink-CBw8P_6_.js";import{r as p,P as A,m as N,x as O,p as I,D as R,g as o,o as r,b as e,i as v,a as c,c as M,w as u,d as x,l as b,n as h,F as D,y as P,t as n,f as z,s as C,L as E,q as S}from"./app-BGh5SFxi.js";const J={class:"min-h-screen bg-black"},U={class:"flex items-center justify-between h-16 bg-gradient-to-r from-red-600 to-red-700 border-b border-red-500"},F=["disabled"],W={class:"mt-6 px-3 sidebar-scroll"},q={class:"space-y-1"},G=["d"],K={key:0,class:"flex-1 nav-text-transition"},Q={class:"font-medium"},X={class:"text-xs text-gray-400 group-hover:text-gray-200"},Y={key:1,class:"absolute left-16 bg-gray-900 text-white px-2 py-1 rounded-md text-xs opacity-0 group-hover:opacity-100 tooltip-optimized whitespace-nowrap z-50"},Z={class:"absolute bottom-0 left-0 right-0 p-3 border-t border-gray-800"},ee={class:"flex-shrink-0 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center"},te={class:"text-sm font-medium text-white"},se={key:0,class:"ml-3 flex-1 min-w-0"},ae={class:"text-sm font-medium text-white truncate"},re={class:"text-xs text-gray-400 truncate"},oe={class:"bg-gradient-to-r from-gray-900 to-black border-b border-gray-800 shadow-lg"},ie={class:"px-4 sm:px-6 lg:px-8"},ne={class:"flex items-center justify-between h-16"},le={class:"flex items-center space-x-4"},de={class:"flex items-center space-x-3 p-2 rounded-lg text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors duration-200"},ce={class:"w-8 h-8 bg-red-600 rounded-full flex items-center justify-center"},ue={class:"text-sm font-medium"},he={class:"hidden md:block text-left"},me={class:"text-sm font-medium"},fe={class:"text-xs text-gray-400"},ve={class:"py-1"},pe={key:0,class:"bg-gradient-to-r from-gray-900 to-black border-b border-gray-800 shadow-sm"},ge={class:"px-4 py-6 sm:px-6 lg:px-8"},xe={class:"bg-black min-h-screen"},ke={__name:"AuthenticatedLayout",setup(be){const l=p(!1),a=p(!1),g=p(!1),d=p(!1),V=A(),w=N(()=>V.url),j=()=>{const s=localStorage.getItem("sidebarCollapsed");s!==null&&(a.value=JSON.parse(s)),S.on("start",()=>{g.value=!0}),S.on("finish",()=>{g.value=!1})},T=[{name:"Dashboard",href:"dashboard",icon:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586l-2 2V6H5v12h7v2H4a1 1 0 01-1-1V4z",description:"Overview and statistics"},{name:"Customers",href:"customers.index",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",description:"Manage customer information"},{name:"Devices",href:"devices.index",icon:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z",description:"Device registration and tracking"},{name:"Repair Orders",href:"repair-orders.index",icon:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",description:"Track repair progress"},{name:"Technicians",href:"technicians.index",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",description:"Manage repair technicians"},{name:"Services",href:"services.index",icon:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",description:"Manage repair services"},{name:"Parts",href:"parts.index",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",description:"Inventory management"},{name:"Invoices",href:"invoices.index",icon:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",description:"Billing and payments"},{name:"Reports",href:"reports.index",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",description:"Analytics and insights"}];let m=null;const H=()=>{d.value||(d.value=!0,l.value=!l.value,setTimeout(()=>{d.value=!1},300))},$=()=>{d.value||(d.value=!0,a.value=!a.value,m&&clearTimeout(m),m=setTimeout(()=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(a.value))},100),a.value&&(l.value=!1),setTimeout(()=>{d.value=!1},300))};let f=null;const y=()=>{f&&clearTimeout(f),f=setTimeout(()=>{window.innerWidth>=1024&&(l.value=!1)},150)},B=s=>w.value.startsWith(route(s));return O(w,()=>{const s=localStorage.getItem("sidebarCollapsed");if(s!==null){const t=JSON.parse(s);a.value!==t&&(a.value=t)}}),I(()=>{j(),window.addEventListener("resize",y,{passive:!0})}),R(()=>{window.removeEventListener("resize",y),m&&clearTimeout(m),f&&clearTimeout(f)}),(s,t)=>(r(),o("div",J,[e("div",{class:h(["fixed inset-y-0 left-0 z-50 bg-black border-r border-gray-800 shadow-2xl sidebar-optimized sidebar-transition reduce-paint",[a.value?"w-16":"w-64",l.value?"translate-x-0":"-translate-x-full lg:translate-x-0"]])},[e("div",U,[a.value?(r(),M(x(b),{key:1,href:s.route("dashboard"),class:"flex items-center justify-center w-full"},{default:u(()=>[c(_,{class:"block h-8 w-auto fill-current text-white"})]),_:1},8,["href"])):(r(),M(x(b),{key:0,href:s.route("dashboard"),class:"flex items-center px-4"},{default:u(()=>[c(_,{class:"block h-8 w-auto fill-current text-white"}),t[1]||(t[1]=e("span",{class:"ml-2 text-xl font-bold text-white"},"VSMART SMS",-1))]),_:1,__:[1]},8,["href"])),e("button",{onClick:$,class:"hidden lg:flex items-center justify-center w-8 h-8 mr-2 text-white hover:bg-red-800 rounded-lg collapse-btn",disabled:d.value},[(r(),o("svg",{class:h(["w-4 h-4 transform transition-transform duration-200",{"rotate-180":a.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[2]||(t[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"},null,-1)]),2))],8,F)]),e("nav",W,[e("div",q,[(r(),o(D,null,P(T,i=>c(x(b),{key:i.name,href:s.route(i.href),class:h(["group flex items-center px-3 py-3 text-sm font-medium rounded-lg nav-item-optimized relative",[B(i.href)?"bg-red-600 text-white shadow-lg":"text-gray-300 hover:bg-red-600 hover:text-white",a.value?"justify-center":""]]),title:a.value?i.name:""},{default:u(()=>[(r(),o("svg",{class:h(["flex-shrink-0 w-5 h-5",a.value?"":"mr-3"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:i.icon},null,8,G)],2)),a.value?v("",!0):(r(),o("div",K,[e("div",Q,n(i.name),1),e("div",X,n(i.description),1)])),a.value?(r(),o("div",Y,n(i.name),1)):v("",!0)]),_:2},1032,["href","class","title"])),64))])]),e("div",Z,[e("div",{class:h(["flex items-center",a.value?"justify-center":""])},[e("div",ee,[e("span",te,n(s.$page.props.auth.user.name.charAt(0)),1)]),a.value?v("",!0):(r(),o("div",se,[e("p",ae,n(s.$page.props.auth.user.name),1),e("p",re,n(s.$page.props.auth.user.role||"User"),1)]))],2)])],2),l.value?(r(),o("div",{key:0,class:"fixed inset-0 z-40 lg:hidden",onClick:t[0]||(t[0]=i=>l.value=!1)},t[3]||(t[3]=[e("div",{class:"absolute inset-0 bg-black bg-opacity-75 mobile-overlay"},null,-1)]))):v("",!0),e("div",{class:h(["main-content-optimized reduce-paint",a.value?"lg:ml-16":"lg:ml-64"])},[e("div",oe,[e("div",ie,[e("div",ne,[e("button",{onClick:H,class:"lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-white hover:text-red-500 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-red-500 transition-colors duration-200"},[(r(),o("svg",{class:h(["h-6 w-6 transform transition-transform duration-200",{"rotate-90":l.value}]),stroke:"currentColor",fill:"none",viewBox:"0 0 24 24"},t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},null,-1)]),2))]),t[9]||(t[9]=e("div",{class:"flex-1 lg:ml-4"},[e("div",{class:"flex items-center space-x-2 text-sm text-gray-400"})],-1)),e("div",le,[c(L,{align:"right",width:"48"},{trigger:u(()=>[e("button",de,[e("div",ce,[e("span",ue,n(s.$page.props.auth.user.name.charAt(0)),1)]),e("div",he,[e("div",me,n(s.$page.props.auth.user.name),1),e("div",fe,n(s.$page.props.auth.user.role||"User"),1)]),t[5]||(t[5]=e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1))])]),content:u(()=>[e("div",ve,[c(k,{href:s.route("profile.edit"),class:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-500 hover:text-white transition-colors duration-200"},{default:u(()=>t[6]||(t[6]=[e("svg",{class:"w-4 h-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),z(" Profile ",-1)])),_:1,__:[6]},8,["href"]),t[8]||(t[8]=e("hr",{class:"border-gray-200"},null,-1)),c(k,{href:s.route("logout"),method:"post",as:"button",class:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-red-500 hover:text-white transition-colors duration-200"},{default:u(()=>t[7]||(t[7]=[e("svg",{class:"w-4 h-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),z(" Log Out ",-1)])),_:1,__:[7]},8,["href"])])]),_:1})])])])]),s.$slots.header?(r(),o("header",pe,[e("div",ge,[C(s.$slots,"header")])])):v("",!0),e("main",xe,[C(s.$slots,"default")])],2),c(E,{show:g.value},null,8,["show"])]))}};export{ke as _};
