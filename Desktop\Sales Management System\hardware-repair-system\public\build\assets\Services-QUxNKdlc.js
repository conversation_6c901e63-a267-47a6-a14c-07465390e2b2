import{_ as w}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as _}from"./Chart-DPkq-W3M.js";import{m as k,g as l,o as d,a as n,d as p,h as C,w as c,b as t,t as o,F as x,y as S,n as g,l as B}from"./app-BGh5SFxi.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const j={class:"flex items-center justify-between"},A={class:"flex items-center space-x-4"},D={class:"flex items-center space-x-3"},L=["value"],F={class:"py-8"},M={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},P={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},z={class:"bg-gradient-to-br from-orange-600 to-orange-700 rounded-xl p-6 text-white shadow-lg"},O={class:"flex items-center justify-between"},R={class:"text-3xl font-bold mt-1"},V={class:"bg-gradient-to-br from-green-600 to-green-700 rounded-xl p-6 text-white shadow-lg"},I={class:"flex items-center justify-between"},T={class:"text-3xl font-bold mt-1"},N={class:"bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-6 text-white shadow-lg"},E={class:"flex items-center justify-between"},U={class:"text-3xl font-bold mt-1"},W={class:"bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl p-6 text-white shadow-lg"},$={class:"flex items-center justify-between"},G={class:"text-2xl font-bold mt-1"},H={class:"text-purple-100 text-xs mt-1"},Y={class:"grid grid-cols-1 gap-6 mb-8"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},q={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-lg"},J={class:"overflow-x-auto"},K={class:"w-full"},Q={class:"divide-y divide-gray-700"},X={class:"px-6 py-4 whitespace-nowrap"},tt={class:"flex items-center"},et={class:"ml-4"},st={class:"text-sm font-medium text-white"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},it={class:"px-6 py-4 whitespace-nowrap"},xt={__name:"Services",props:{data:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(a){const m=a,i=r=>"₱"+parseFloat(r||0).toLocaleString("en-US",{minimumFractionDigits:2}),f=r=>`${r.toFixed(1)}%`,h=k(()=>{const r=m.charts.popularity||[];return{labels:r.map(e=>e.name),datasets:[{label:"Orders Count",data:r.map(e=>parseInt(e.count||0)),backgroundColor:["rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(249, 115, 22, 0.8)","rgba(168, 85, 247, 0.8)","rgba(14, 165, 233, 0.8)","rgba(132, 204, 22, 0.8)"],borderWidth:2}]}}),b={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}},v=r=>{window.location.href=route("reports.services",{period:r})},y=(r,e)=>{const s=r/(e||1);return s>=10?"text-green-400":s>=5?"text-blue-400":s>=2?"text-yellow-400":"text-gray-400"},u=(r,e)=>{const s=r/(e||1);return s>=10?{text:"Excellent",class:"bg-green-100 text-green-800"}:s>=5?{text:"Good",class:"bg-blue-100 text-blue-800"}:s>=2?{text:"Average",class:"bg-yellow-100 text-yellow-800"}:{text:"Low",class:"bg-gray-100 text-gray-800"}};return(r,e)=>(d(),l(x,null,[n(p(C),{title:"Services Analytics"}),n(w,null,{header:c(()=>[t("div",j,[t("div",A,[n(p(B),{href:r.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:c(()=>e[1]||(e[1]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),e[2]||(e[2]=t("div",null,[t("h2",{class:"text-2xl font-bold leading-tight text-white"}," Services Analytics "),t("p",{class:"text-sm text-gray-400 mt-1"}," Service performance and popularity metrics ")],-1))]),t("div",D,[t("select",{value:a.period,onChange:e[0]||(e[0]=s=>v(s.target.value)),class:"bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},e[3]||(e[3]=[t("option",{value:"7days"},"Last 7 Days",-1),t("option",{value:"30days"},"Last 30 Days",-1),t("option",{value:"90days"},"Last 90 Days",-1),t("option",{value:"thisyear"},"This Year",-1)]),40,L)])])]),default:c(()=>[t("div",F,[t("div",M,[t("div",P,[t("div",z,[t("div",O,[t("div",null,[e[4]||(e[4]=t("p",{class:"text-orange-100 text-sm font-medium"},"Total Services",-1)),t("p",R,o(a.data.total_services?.toLocaleString()),1)]),e[5]||(e[5]=t("div",{class:"p-3 bg-orange-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-orange-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"})])],-1))])]),t("div",V,[t("div",I,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-green-100 text-sm font-medium"},"Active Services",-1)),t("p",T,o(a.data.active_services?.toLocaleString()),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-green-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),t("div",N,[t("div",E,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-blue-100 text-sm font-medium"},"Service Utilization",-1)),t("p",U,o(f(a.data.active_services/a.data.total_services*100)),1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-blue-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),t("div",W,[t("div",$,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-purple-100 text-sm font-medium"},"Top Service Revenue",-1)),t("p",G,o(i(a.data.service_performance?.[0]?.revenue||0)),1),t("p",H,o(a.data.service_performance?.[0]?.name||"N/A"),1)]),e[11]||(e[11]=t("div",{class:"p-3 bg-purple-500 bg-opacity-30 rounded-lg"},[t("svg",{class:"w-8 h-8 text-purple-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1))])])]),t("div",Y,[t("div",Z,[e[12]||(e[12]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Most Popular Services",-1)),n(_,{type:"bar",data:h.value,options:b,height:400},null,8,["data"])])]),t("div",q,[e[15]||(e[15]=t("div",{class:"p-6 border-b border-gray-700"},[t("h3",{class:"text-lg font-semibold text-white"},"Service Performance Analysis")],-1)),t("div",J,[t("table",K,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Service"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Base Price"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Total Revenue"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Orders"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg. Revenue/Order"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Performance")])],-1)),t("tbody",Q,[(d(!0),l(x,null,S(a.data.service_performance,s=>(d(),l("tr",{key:s.name,class:"hover:bg-gray-700 transition-colors duration-200"},[t("td",X,[t("div",tt,[e[13]||(e[13]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-orange-600 flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"})])])],-1)),t("div",et,[t("div",st,o(s.name),1)])])]),t("td",rt,o(i(s.price)),1),t("td",{class:g(["px-6 py-4 whitespace-nowrap text-sm font-semibold",y(s.revenue,s.price)])},o(i(s.revenue)),3),t("td",ot,o(s.orders),1),t("td",at,o(s.orders>0?i(s.revenue/s.orders):"₱0.00"),1),t("td",it,[t("span",{class:g(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",u(s.revenue,s.price).class])},o(u(s.revenue,s.price).text),3)])]))),128))])])])])])])]),_:1})],64))}};export{xt as default};
