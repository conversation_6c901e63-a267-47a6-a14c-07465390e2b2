import{f as He,C as ie,a as Ie,b as Le,P as Qe,c as Re,B as Be,p as Ae,d as Xe,e as Ge,A as $e,T as je}from"./chart-C26Vmg0g.js";import{_ as Ue,r as ze,p as Ve,G as Ze,D as Je,x as ue,g as Ke,o as Se,b as et,J as tt}from"./app-BGh5SFxi.js";const we=6048e5,nt=864e5,L=6e4,Q=36e5,rt=1e3,ce=Symbol.for("constructDateFrom");function g(r,e){return typeof r=="function"?r(e):r&&typeof r=="object"&&ce in r?r[ce](e):r instanceof Date?new r.constructor(e):new Date(e)}function u(r,e){return g(e||r,r)}function G(r,e,t){const n=u(r,t?.in);return isNaN(e)?g(t?.in||r,NaN):(e&&n.setDate(n.getDate()+e),n)}function J(r,e,t){const n=u(r,t?.in);if(isNaN(e))return g(r,NaN);if(!e)return n;const a=n.getDate(),s=g(r,n.getTime());s.setMonth(n.getMonth()+e+1,0);const o=s.getDate();return a>=o?s:(n.setFullYear(s.getFullYear(),s.getMonth(),a),n)}function K(r,e,t){return g(r,+u(r)+e)}function at(r,e,t){return K(r,e*Q)}let st={};function v(){return st}function O(r,e){const t=v(),n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,a=u(r,e?.in),s=a.getDay(),o=(s<n?7:0)+s-n;return a.setDate(a.getDate()-o),a.setHours(0,0,0,0),a}function E(r,e){return O(r,{...e,weekStartsOn:1})}function ye(r,e){const t=u(r,e?.in),n=t.getFullYear(),a=g(t,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);const s=E(a),o=g(t,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const i=E(o);return t.getTime()>=s.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function A(r){const e=u(r),t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+r-+t}function W(r,...e){const t=g.bind(null,e.find(n=>typeof n=="object"));return e.map(t)}function z(r,e){const t=u(r,e?.in);return t.setHours(0,0,0,0),t}function ge(r,e,t){const[n,a]=W(t?.in,r,e),s=z(n),o=z(a),i=+s-A(s),c=+o-A(o);return Math.round((i-c)/nt)}function ot(r,e){const t=ye(r,e),n=g(r,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),E(n)}function it(r,e,t){const n=u(r,t?.in);return n.setTime(n.getTime()+e*L),n}function ut(r,e,t){return J(r,e*3,t)}function ct(r,e,t){return K(r,e*1e3)}function dt(r,e,t){return G(r,e*7,t)}function lt(r,e,t){return J(r,e*12,t)}function I(r,e){const t=+u(r)-+u(e);return t<0?-1:t>0?1:t}function ft(r){return r instanceof Date||typeof r=="object"&&Object.prototype.toString.call(r)==="[object Date]"}function pe(r){return!(!ft(r)&&typeof r!="number"||isNaN(+u(r)))}function ht(r,e,t){const[n,a]=W(t?.in,r,e),s=n.getFullYear()-a.getFullYear(),o=n.getMonth()-a.getMonth();return s*12+o}function mt(r,e,t){const[n,a]=W(t?.in,r,e);return n.getFullYear()-a.getFullYear()}function be(r,e,t){const[n,a]=W(t?.in,r,e),s=de(n,a),o=Math.abs(ge(n,a));n.setDate(n.getDate()-s*o);const i=+(de(n,a)===-s),c=s*(o-i);return c===0?0:c}function de(r,e){const t=r.getFullYear()-e.getFullYear()||r.getMonth()-e.getMonth()||r.getDate()-e.getDate()||r.getHours()-e.getHours()||r.getMinutes()-e.getMinutes()||r.getSeconds()-e.getSeconds()||r.getMilliseconds()-e.getMilliseconds();return t<0?-1:t>0?1:t}function R(r){return e=>{const n=(r?Math[r]:Math.trunc)(e);return n===0?0:n}}function wt(r,e,t){const[n,a]=W(t?.in,r,e),s=(+n-+a)/Q;return R(t?.roundingMethod)(s)}function S(r,e){return+u(r)-+u(e)}function yt(r,e,t){const n=S(r,e)/L;return R(t?.roundingMethod)(n)}function xe(r,e){const t=u(r,e?.in);return t.setHours(23,59,59,999),t}function Me(r,e){const t=u(r,e?.in),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function gt(r,e){const t=u(r,e?.in);return+xe(t,e)==+Me(t,e)}function De(r,e,t){const[n,a,s]=W(t?.in,r,r,e),o=I(a,s),i=Math.abs(ht(a,s));if(i<1)return 0;a.getMonth()===1&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-o*i);let c=I(a,s)===-o;gt(n)&&i===1&&I(n,s)===1&&(c=!1);const d=o*(i-+c);return d===0?0:d}function pt(r,e,t){const n=De(r,e,t)/3;return R(t?.roundingMethod)(n)}function bt(r,e,t){const n=S(r,e)/1e3;return R(t?.roundingMethod)(n)}function xt(r,e,t){const n=be(r,e,t)/7;return R(t?.roundingMethod)(n)}function Mt(r,e,t){const[n,a]=W(t?.in,r,e),s=I(n,a),o=Math.abs(mt(n,a));n.setFullYear(1584),a.setFullYear(1584);const i=I(n,a)===-s,c=s*(o-+i);return c===0?0:c}function Dt(r,e){const t=u(r,e?.in),n=t.getMonth(),a=n-n%3;return t.setMonth(a,1),t.setHours(0,0,0,0),t}function kt(r,e){const t=u(r,e?.in);return t.setDate(1),t.setHours(0,0,0,0),t}function Tt(r,e){const t=u(r,e?.in),n=t.getFullYear();return t.setFullYear(n+1,0,0),t.setHours(23,59,59,999),t}function ke(r,e){const t=u(r,e?.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function Ot(r,e){const t=u(r,e?.in);return t.setMinutes(59,59,999),t}function Pt(r,e){const t=v(),n=t.weekStartsOn??t.locale?.options?.weekStartsOn??0,a=u(r,e?.in),s=a.getDay(),o=(s<n?-7:0)+6-(s-n);return a.setDate(a.getDate()+o),a.setHours(23,59,59,999),a}function Yt(r,e){const t=u(r,e?.in);return t.setSeconds(59,999),t}function _t(r,e){const t=u(r,e?.in),n=t.getMonth(),a=n-n%3+3;return t.setMonth(a,0),t.setHours(23,59,59,999),t}function vt(r,e){const t=u(r,e?.in);return t.setMilliseconds(999),t}const Wt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Nt=(r,e,t)=>{let n;const a=Wt[r];return typeof a=="string"?n=a:e===1?n=a.one:n=a.other.replace("{{count}}",e.toString()),t?.addSuffix?t.comparison&&t.comparison>0?"in "+n:n+" ago":n};function j(r){return(e={})=>{const t=e.width?String(e.width):r.defaultWidth;return r.formats[t]||r.formats[r.defaultWidth]}}const Ct={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Et={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},qt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ft={date:j({formats:Ct,defaultWidth:"full"}),time:j({formats:Et,defaultWidth:"full"}),dateTime:j({formats:qt,defaultWidth:"full"})},Ht={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},It=(r,e,t,n)=>Ht[r];function q(r){return(e,t)=>{const n=t?.context?String(t.context):"standalone";let a;if(n==="formatting"&&r.formattingValues){const o=r.defaultFormattingWidth||r.defaultWidth,i=t?.width?String(t.width):o;a=r.formattingValues[i]||r.formattingValues[o]}else{const o=r.defaultWidth,i=t?.width?String(t.width):r.defaultWidth;a=r.values[i]||r.values[o]}const s=r.argumentCallback?r.argumentCallback(e):e;return a[s]}}const Lt={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Qt={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Rt={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Bt={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},At={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Xt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Gt=(r,e)=>{const t=Number(r),n=t%100;if(n>20||n<10)switch(n%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},$t={ordinalNumber:Gt,era:q({values:Lt,defaultWidth:"wide"}),quarter:q({values:Qt,defaultWidth:"wide",argumentCallback:r=>r-1}),month:q({values:Rt,defaultWidth:"wide"}),day:q({values:Bt,defaultWidth:"wide"}),dayPeriod:q({values:At,defaultWidth:"wide",formattingValues:Xt,defaultFormattingWidth:"wide"})};function F(r){return(e,t={})=>{const n=t.width,a=n&&r.matchPatterns[n]||r.matchPatterns[r.defaultMatchWidth],s=e.match(a);if(!s)return null;const o=s[0],i=n&&r.parsePatterns[n]||r.parsePatterns[r.defaultParseWidth],c=Array.isArray(i)?Ut(i,M=>M.test(o)):jt(i,M=>M.test(o));let d;d=r.valueCallback?r.valueCallback(c):c,d=t.valueCallback?t.valueCallback(d):d;const m=e.slice(o.length);return{value:d,rest:m}}}function jt(r,e){for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)&&e(r[t]))return t}function Ut(r,e){for(let t=0;t<r.length;t++)if(e(r[t]))return t}function zt(r){return(e,t={})=>{const n=e.match(r.matchPattern);if(!n)return null;const a=n[0],s=e.match(r.parsePattern);if(!s)return null;let o=r.valueCallback?r.valueCallback(s[0]):s[0];o=t.valueCallback?t.valueCallback(o):o;const i=e.slice(a.length);return{value:o,rest:i}}}const Vt=/^(\d+)(th|st|nd|rd)?/i,Zt=/\d+/i,Jt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Kt={any:[/^b/i,/^(a|c)/i]},St={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},en={any:[/1/i,/2/i,/3/i,/4/i]},tn={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},nn={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},rn={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},an={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},sn={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},on={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},un={ordinalNumber:zt({matchPattern:Vt,parsePattern:Zt,valueCallback:r=>parseInt(r,10)}),era:F({matchPatterns:Jt,defaultMatchWidth:"wide",parsePatterns:Kt,defaultParseWidth:"any"}),quarter:F({matchPatterns:St,defaultMatchWidth:"wide",parsePatterns:en,defaultParseWidth:"any",valueCallback:r=>r+1}),month:F({matchPatterns:tn,defaultMatchWidth:"wide",parsePatterns:nn,defaultParseWidth:"any"}),day:F({matchPatterns:rn,defaultMatchWidth:"wide",parsePatterns:an,defaultParseWidth:"any"}),dayPeriod:F({matchPatterns:sn,defaultMatchWidth:"any",parsePatterns:on,defaultParseWidth:"any"})},Te={code:"en-US",formatDistance:Nt,formatLong:Ft,formatRelative:It,localize:$t,match:un,options:{weekStartsOn:0,firstWeekContainsDate:1}};function cn(r,e){const t=u(r,e?.in);return ge(t,ke(t))+1}function Oe(r,e){const t=u(r,e?.in),n=+E(t)-+ot(t);return Math.round(n/we)+1}function ee(r,e){const t=u(r,e?.in),n=t.getFullYear(),a=v(),s=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,o=g(e?.in||r,0);o.setFullYear(n+1,0,s),o.setHours(0,0,0,0);const i=O(o,e),c=g(e?.in||r,0);c.setFullYear(n,0,s),c.setHours(0,0,0,0);const d=O(c,e);return+t>=+i?n+1:+t>=+d?n:n-1}function dn(r,e){const t=v(),n=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??t.firstWeekContainsDate??t.locale?.options?.firstWeekContainsDate??1,a=ee(r,e),s=g(e?.in||r,0);return s.setFullYear(a,0,n),s.setHours(0,0,0,0),O(s,e)}function Pe(r,e){const t=u(r,e?.in),n=+O(t,e)-+dn(t,e);return Math.round(n/we)+1}function h(r,e){const t=r<0?"-":"",n=Math.abs(r).toString().padStart(e,"0");return t+n}const P={y(r,e){const t=r.getFullYear(),n=t>0?t:1-t;return h(e==="yy"?n%100:n,e.length)},M(r,e){const t=r.getMonth();return e==="M"?String(t+1):h(t+1,2)},d(r,e){return h(r.getDate(),e.length)},a(r,e){const t=r.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(r,e){return h(r.getHours()%12||12,e.length)},H(r,e){return h(r.getHours(),e.length)},m(r,e){return h(r.getMinutes(),e.length)},s(r,e){return h(r.getSeconds(),e.length)},S(r,e){const t=e.length,n=r.getMilliseconds(),a=Math.trunc(n*Math.pow(10,t-3));return h(a,e.length)}},C={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},le={G:function(r,e,t){const n=r.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(n,{width:"abbreviated"});case"GGGGG":return t.era(n,{width:"narrow"});case"GGGG":default:return t.era(n,{width:"wide"})}},y:function(r,e,t){if(e==="yo"){const n=r.getFullYear(),a=n>0?n:1-n;return t.ordinalNumber(a,{unit:"year"})}return P.y(r,e)},Y:function(r,e,t,n){const a=ee(r,n),s=a>0?a:1-a;if(e==="YY"){const o=s%100;return h(o,2)}return e==="Yo"?t.ordinalNumber(s,{unit:"year"}):h(s,e.length)},R:function(r,e){const t=ye(r);return h(t,e.length)},u:function(r,e){const t=r.getFullYear();return h(t,e.length)},Q:function(r,e,t){const n=Math.ceil((r.getMonth()+1)/3);switch(e){case"Q":return String(n);case"QQ":return h(n,2);case"Qo":return t.ordinalNumber(n,{unit:"quarter"});case"QQQ":return t.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(n,{width:"wide",context:"formatting"})}},q:function(r,e,t){const n=Math.ceil((r.getMonth()+1)/3);switch(e){case"q":return String(n);case"qq":return h(n,2);case"qo":return t.ordinalNumber(n,{unit:"quarter"});case"qqq":return t.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(n,{width:"wide",context:"standalone"})}},M:function(r,e,t){const n=r.getMonth();switch(e){case"M":case"MM":return P.M(r,e);case"Mo":return t.ordinalNumber(n+1,{unit:"month"});case"MMM":return t.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(n,{width:"wide",context:"formatting"})}},L:function(r,e,t){const n=r.getMonth();switch(e){case"L":return String(n+1);case"LL":return h(n+1,2);case"Lo":return t.ordinalNumber(n+1,{unit:"month"});case"LLL":return t.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(n,{width:"wide",context:"standalone"})}},w:function(r,e,t,n){const a=Pe(r,n);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):h(a,e.length)},I:function(r,e,t){const n=Oe(r);return e==="Io"?t.ordinalNumber(n,{unit:"week"}):h(n,e.length)},d:function(r,e,t){return e==="do"?t.ordinalNumber(r.getDate(),{unit:"date"}):P.d(r,e)},D:function(r,e,t){const n=cn(r);return e==="Do"?t.ordinalNumber(n,{unit:"dayOfYear"}):h(n,e.length)},E:function(r,e,t){const n=r.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(n,{width:"short",context:"formatting"});case"EEEE":default:return t.day(n,{width:"wide",context:"formatting"})}},e:function(r,e,t,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return h(s,2);case"eo":return t.ordinalNumber(s,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});case"eeee":default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(r,e,t,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return h(s,e.length);case"co":return t.ordinalNumber(s,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});case"cccc":default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(r,e,t){const n=r.getDay(),a=n===0?7:n;switch(e){case"i":return String(a);case"ii":return h(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(n,{width:"short",context:"formatting"});case"iiii":default:return t.day(n,{width:"wide",context:"formatting"})}},a:function(r,e,t){const a=r.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(r,e,t){const n=r.getHours();let a;switch(n===12?a=C.noon:n===0?a=C.midnight:a=n/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(r,e,t){const n=r.getHours();let a;switch(n>=17?a=C.evening:n>=12?a=C.afternoon:n>=4?a=C.morning:a=C.night,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(r,e,t){if(e==="ho"){let n=r.getHours()%12;return n===0&&(n=12),t.ordinalNumber(n,{unit:"hour"})}return P.h(r,e)},H:function(r,e,t){return e==="Ho"?t.ordinalNumber(r.getHours(),{unit:"hour"}):P.H(r,e)},K:function(r,e,t){const n=r.getHours()%12;return e==="Ko"?t.ordinalNumber(n,{unit:"hour"}):h(n,e.length)},k:function(r,e,t){let n=r.getHours();return n===0&&(n=24),e==="ko"?t.ordinalNumber(n,{unit:"hour"}):h(n,e.length)},m:function(r,e,t){return e==="mo"?t.ordinalNumber(r.getMinutes(),{unit:"minute"}):P.m(r,e)},s:function(r,e,t){return e==="so"?t.ordinalNumber(r.getSeconds(),{unit:"second"}):P.s(r,e)},S:function(r,e){return P.S(r,e)},X:function(r,e,t){const n=r.getTimezoneOffset();if(n===0)return"Z";switch(e){case"X":return he(n);case"XXXX":case"XX":return _(n);case"XXXXX":case"XXX":default:return _(n,":")}},x:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"x":return he(n);case"xxxx":case"xx":return _(n);case"xxxxx":case"xxx":default:return _(n,":")}},O:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+fe(n,":");case"OOOO":default:return"GMT"+_(n,":")}},z:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+fe(n,":");case"zzzz":default:return"GMT"+_(n,":")}},t:function(r,e,t){const n=Math.trunc(+r/1e3);return h(n,e.length)},T:function(r,e,t){return h(+r,e.length)}};function fe(r,e=""){const t=r>0?"-":"+",n=Math.abs(r),a=Math.trunc(n/60),s=n%60;return s===0?t+String(a):t+String(a)+e+h(s,2)}function he(r,e){return r%60===0?(r>0?"-":"+")+h(Math.abs(r)/60,2):_(r,e)}function _(r,e=""){const t=r>0?"-":"+",n=Math.abs(r),a=h(Math.trunc(n/60),2),s=h(n%60,2);return t+a+e+s}const me=(r,e)=>{switch(r){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},Ye=(r,e)=>{switch(r){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},ln=(r,e)=>{const t=r.match(/(P+)(p+)?/)||[],n=t[1],a=t[2];if(!a)return me(r,e);let s;switch(n){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;case"PPPP":default:s=e.dateTime({width:"full"});break}return s.replace("{{date}}",me(n,e)).replace("{{time}}",Ye(a,e))},V={p:Ye,P:ln},fn=/^D+$/,hn=/^Y+$/,mn=["D","DD","YY","YYYY"];function _e(r){return fn.test(r)}function ve(r){return hn.test(r)}function Z(r,e,t){const n=wn(r,e,t);if(console.warn(n),mn.includes(r))throw new RangeError(n)}function wn(r,e,t){const n=r[0]==="Y"?"years":"days of the month";return`Use \`${r.toLowerCase()}\` instead of \`${r}\` (in \`${e}\`) for formatting ${n} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const yn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,gn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,pn=/^'([^]*?)'?$/,bn=/''/g,xn=/[a-zA-Z]/;function Mn(r,e,t){const n=v(),a=t?.locale??n.locale??Te,s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,o=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=u(r,t?.in);if(!pe(i))throw new RangeError("Invalid time value");let c=e.match(gn).map(m=>{const M=m[0];if(M==="p"||M==="P"){const Y=V[M];return Y(m,a.formatLong)}return m}).join("").match(yn).map(m=>{if(m==="''")return{isToken:!1,value:"'"};const M=m[0];if(M==="'")return{isToken:!1,value:Dn(m)};if(le[M])return{isToken:!0,value:m};if(M.match(xn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+M+"`");return{isToken:!1,value:m}});a.localize.preprocessor&&(c=a.localize.preprocessor(i,c));const d={firstWeekContainsDate:s,weekStartsOn:o,locale:a};return c.map(m=>{if(!m.isToken)return m.value;const M=m.value;(!t?.useAdditionalWeekYearTokens&&ve(M)||!t?.useAdditionalDayOfYearTokens&&_e(M))&&Z(M,e,String(r));const Y=le[M[0]];return Y(i,M,a.localize,d)}).join("")}function Dn(r){const e=r.match(pn);return e?e[1].replace(bn,"'"):r}function kn(){return Object.assign({},v())}function Tn(r,e){const t=u(r,e?.in).getDay();return t===0?7:t}function On(r,e){const t=Pn(e)?new e(0):g(e,0);return t.setFullYear(r.getFullYear(),r.getMonth(),r.getDate()),t.setHours(r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()),t}function Pn(r){return typeof r=="function"&&r.prototype?.constructor===r}const Yn=10;class We{subPriority=0;validate(e,t){return!0}}class _n extends We{constructor(e,t,n,a,s){super(),this.value=e,this.validateValue=t,this.setValue=n,this.priority=a,s&&(this.subPriority=s)}validate(e,t){return this.validateValue(e,this.value,t)}set(e,t,n){return this.setValue(e,t,this.value,n)}}class vn extends We{priority=Yn;subPriority=-1;constructor(e,t){super(),this.context=e||(n=>g(t,n))}set(e,t){return t.timestampIsSet?e:g(e,On(e,this.context))}}class f{run(e,t,n,a){const s=this.parse(e,t,n,a);return s?{setter:new _n(s.value,this.validate,this.set,this.priority,this.subPriority),rest:s.rest}:null}validate(e,t,n){return!0}}class Wn extends f{priority=140;parse(e,t,n){switch(t){case"G":case"GG":case"GGG":return n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"});case"GGGGG":return n.era(e,{width:"narrow"});case"GGGG":default:return n.era(e,{width:"wide"})||n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"})}}set(e,t,n){return t.era=n,e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}incompatibleTokens=["R","u","t","T"]}const b={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},k={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function x(r,e){return r&&{value:e(r.value),rest:r.rest}}function w(r,e){const t=e.match(r);return t?{value:parseInt(t[0],10),rest:e.slice(t[0].length)}:null}function T(r,e){const t=e.match(r);if(!t)return null;if(t[0]==="Z")return{value:0,rest:e.slice(1)};const n=t[1]==="+"?1:-1,a=t[2]?parseInt(t[2],10):0,s=t[3]?parseInt(t[3],10):0,o=t[5]?parseInt(t[5],10):0;return{value:n*(a*Q+s*L+o*rt),rest:e.slice(t[0].length)}}function Ne(r){return w(b.anyDigitsSigned,r)}function p(r,e){switch(r){case 1:return w(b.singleDigit,e);case 2:return w(b.twoDigits,e);case 3:return w(b.threeDigits,e);case 4:return w(b.fourDigits,e);default:return w(new RegExp("^\\d{1,"+r+"}"),e)}}function X(r,e){switch(r){case 1:return w(b.singleDigitSigned,e);case 2:return w(b.twoDigitsSigned,e);case 3:return w(b.threeDigitsSigned,e);case 4:return w(b.fourDigitsSigned,e);default:return w(new RegExp("^-?\\d{1,"+r+"}"),e)}}function te(r){switch(r){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function Ce(r,e){const t=e>0,n=t?e:1-e;let a;if(n<=50)a=r||100;else{const s=n+50,o=Math.trunc(s/100)*100,i=r>=s%100;a=r+o-(i?100:0)}return t?a:1-a}function Ee(r){return r%400===0||r%4===0&&r%100!==0}class Nn extends f{priority=130;incompatibleTokens=["Y","R","u","w","I","i","e","c","t","T"];parse(e,t,n){const a=s=>({year:s,isTwoDigitYear:t==="yy"});switch(t){case"y":return x(p(4,e),a);case"yo":return x(n.ordinalNumber(e,{unit:"year"}),a);default:return x(p(t.length,e),a)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n){const a=e.getFullYear();if(n.isTwoDigitYear){const o=Ce(n.year,a);return e.setFullYear(o,0,1),e.setHours(0,0,0,0),e}const s=!("era"in t)||t.era===1?n.year:1-n.year;return e.setFullYear(s,0,1),e.setHours(0,0,0,0),e}}class Cn extends f{priority=130;parse(e,t,n){const a=s=>({year:s,isTwoDigitYear:t==="YY"});switch(t){case"Y":return x(p(4,e),a);case"Yo":return x(n.ordinalNumber(e,{unit:"year"}),a);default:return x(p(t.length,e),a)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n,a){const s=ee(e,a);if(n.isTwoDigitYear){const i=Ce(n.year,s);return e.setFullYear(i,0,a.firstWeekContainsDate),e.setHours(0,0,0,0),O(e,a)}const o=!("era"in t)||t.era===1?n.year:1-n.year;return e.setFullYear(o,0,a.firstWeekContainsDate),e.setHours(0,0,0,0),O(e,a)}incompatibleTokens=["y","R","u","Q","q","M","L","I","d","D","i","t","T"]}class En extends f{priority=130;parse(e,t){return X(t==="R"?4:t.length,e)}set(e,t,n){const a=g(e,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),E(a)}incompatibleTokens=["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]}class qn extends f{priority=130;parse(e,t){return X(t==="u"?4:t.length,e)}set(e,t,n){return e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}incompatibleTokens=["G","y","Y","R","w","I","i","e","c","t","T"]}class Fn extends f{priority=120;parse(e,t,n){switch(t){case"Q":case"QQ":return p(t.length,e);case"Qo":return n.ordinalNumber(e,{unit:"quarter"});case"QQQ":return n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(e,{width:"wide",context:"formatting"})||n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]}class Hn extends f{priority=120;parse(e,t,n){switch(t){case"q":case"qq":return p(t.length,e);case"qo":return n.ordinalNumber(e,{unit:"quarter"});case"qqq":return n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(e,{width:"wide",context:"standalone"})||n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]}class In extends f{incompatibleTokens=["Y","R","q","Q","L","w","I","D","i","e","c","t","T"];priority=110;parse(e,t,n){const a=s=>s-1;switch(t){case"M":return x(w(b.month,e),a);case"MM":return x(p(2,e),a);case"Mo":return x(n.ordinalNumber(e,{unit:"month"}),a);case"MMM":return n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(e,{width:"wide",context:"formatting"})||n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}}class Ln extends f{priority=110;parse(e,t,n){const a=s=>s-1;switch(t){case"L":return x(w(b.month,e),a);case"LL":return x(p(2,e),a);case"Lo":return x(n.ordinalNumber(e,{unit:"month"}),a);case"LLL":return n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(e,{width:"wide",context:"standalone"})||n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]}function Qn(r,e,t){const n=u(r,t?.in),a=Pe(n,t)-e;return n.setDate(n.getDate()-a*7),u(n,t?.in)}class Rn extends f{priority=100;parse(e,t,n){switch(t){case"w":return w(b.week,e);case"wo":return n.ordinalNumber(e,{unit:"week"});default:return p(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n,a){return O(Qn(e,n,a),a)}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","i","t","T"]}function Bn(r,e,t){const n=u(r,t?.in),a=Oe(n,t)-e;return n.setDate(n.getDate()-a*7),n}class An extends f{priority=100;parse(e,t,n){switch(t){case"I":return w(b.week,e);case"Io":return n.ordinalNumber(e,{unit:"week"});default:return p(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n){return E(Bn(e,n))}incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]}const Xn=[31,28,31,30,31,30,31,31,30,31,30,31],Gn=[31,29,31,30,31,30,31,31,30,31,30,31];class $n extends f{priority=90;subPriority=1;parse(e,t,n){switch(t){case"d":return w(b.date,e);case"do":return n.ordinalNumber(e,{unit:"date"});default:return p(t.length,e)}}validate(e,t){const n=e.getFullYear(),a=Ee(n),s=e.getMonth();return a?t>=1&&t<=Gn[s]:t>=1&&t<=Xn[s]}set(e,t,n){return e.setDate(n),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","w","I","D","i","e","c","t","T"]}class jn extends f{priority=90;subpriority=1;parse(e,t,n){switch(t){case"D":case"DD":return w(b.dayOfYear,e);case"Do":return n.ordinalNumber(e,{unit:"date"});default:return p(t.length,e)}}validate(e,t){const n=e.getFullYear();return Ee(n)?t>=1&&t<=366:t>=1&&t<=365}set(e,t,n){return e.setMonth(0,n),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]}function ne(r,e,t){const n=v(),a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=u(r,t?.in),o=s.getDay(),c=(e%7+7)%7,d=7-a,m=e<0||e>6?e-(o+d)%7:(c+d)%7-(o+d)%7;return G(s,m,t)}class Un extends f{priority=90;parse(e,t,n){switch(t){case"E":case"EE":case"EEE":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=ne(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["D","i","e","c","t","T"]}class zn extends f{priority=90;parse(e,t,n,a){const s=o=>{const i=Math.floor((o-1)/7)*7;return(o+a.weekStartsOn+6)%7+i};switch(t){case"e":case"ee":return x(p(t.length,e),s);case"eo":return x(n.ordinalNumber(e,{unit:"day"}),s);case"eee":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeeee":return n.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=ne(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]}class Vn extends f{priority=90;parse(e,t,n,a){const s=o=>{const i=Math.floor((o-1)/7)*7;return(o+a.weekStartsOn+6)%7+i};switch(t){case"c":case"cc":return x(p(t.length,e),s);case"co":return x(n.ordinalNumber(e,{unit:"day"}),s);case"ccc":return n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"ccccc":return n.day(e,{width:"narrow",context:"standalone"});case"cccccc":return n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return n.day(e,{width:"wide",context:"standalone"})||n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=ne(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]}function Zn(r,e,t){const n=u(r,t?.in),a=Tn(n,t),s=e-a;return G(n,s,t)}class Jn extends f{priority=90;parse(e,t,n){const a=s=>s===0?7:s;switch(t){case"i":case"ii":return p(t.length,e);case"io":return n.ordinalNumber(e,{unit:"day"});case"iii":return x(n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a);case"iiiii":return x(n.day(e,{width:"narrow",context:"formatting"}),a);case"iiiiii":return x(n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a);case"iiii":default:return x(n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a)}}validate(e,t){return t>=1&&t<=7}set(e,t,n){return e=Zn(e,n),e.setHours(0,0,0,0),e}incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]}class Kn extends f{priority=80;parse(e,t,n){switch(t){case"a":case"aa":case"aaa":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(te(n),0,0,0),e}incompatibleTokens=["b","B","H","k","t","T"]}class Sn extends f{priority=80;parse(e,t,n){switch(t){case"b":case"bb":case"bbb":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(te(n),0,0,0),e}incompatibleTokens=["a","B","H","k","t","T"]}class er extends f{priority=80;parse(e,t,n){switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(te(n),0,0,0),e}incompatibleTokens=["a","b","t","T"]}class tr extends f{priority=70;parse(e,t,n){switch(t){case"h":return w(b.hour12h,e);case"ho":return n.ordinalNumber(e,{unit:"hour"});default:return p(t.length,e)}}validate(e,t){return t>=1&&t<=12}set(e,t,n){const a=e.getHours()>=12;return a&&n<12?e.setHours(n+12,0,0,0):!a&&n===12?e.setHours(0,0,0,0):e.setHours(n,0,0,0),e}incompatibleTokens=["H","K","k","t","T"]}class nr extends f{priority=70;parse(e,t,n){switch(t){case"H":return w(b.hour23h,e);case"Ho":return n.ordinalNumber(e,{unit:"hour"});default:return p(t.length,e)}}validate(e,t){return t>=0&&t<=23}set(e,t,n){return e.setHours(n,0,0,0),e}incompatibleTokens=["a","b","h","K","k","t","T"]}class rr extends f{priority=70;parse(e,t,n){switch(t){case"K":return w(b.hour11h,e);case"Ko":return n.ordinalNumber(e,{unit:"hour"});default:return p(t.length,e)}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.getHours()>=12&&n<12?e.setHours(n+12,0,0,0):e.setHours(n,0,0,0),e}incompatibleTokens=["h","H","k","t","T"]}class ar extends f{priority=70;parse(e,t,n){switch(t){case"k":return w(b.hour24h,e);case"ko":return n.ordinalNumber(e,{unit:"hour"});default:return p(t.length,e)}}validate(e,t){return t>=1&&t<=24}set(e,t,n){const a=n<=24?n%24:n;return e.setHours(a,0,0,0),e}incompatibleTokens=["a","b","h","H","K","t","T"]}class sr extends f{priority=60;parse(e,t,n){switch(t){case"m":return w(b.minute,e);case"mo":return n.ordinalNumber(e,{unit:"minute"});default:return p(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setMinutes(n,0,0),e}incompatibleTokens=["t","T"]}class or extends f{priority=50;parse(e,t,n){switch(t){case"s":return w(b.second,e);case"so":return n.ordinalNumber(e,{unit:"second"});default:return p(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setSeconds(n,0),e}incompatibleTokens=["t","T"]}class ir extends f{priority=30;parse(e,t){const n=a=>Math.trunc(a*Math.pow(10,-t.length+3));return x(p(t.length,e),n)}set(e,t,n){return e.setMilliseconds(n),e}incompatibleTokens=["t","T"]}class ur extends f{priority=10;parse(e,t){switch(t){case"X":return T(k.basicOptionalMinutes,e);case"XX":return T(k.basic,e);case"XXXX":return T(k.basicOptionalSeconds,e);case"XXXXX":return T(k.extendedOptionalSeconds,e);case"XXX":default:return T(k.extended,e)}}set(e,t,n){return t.timestampIsSet?e:g(e,e.getTime()-A(e)-n)}incompatibleTokens=["t","T","x"]}class cr extends f{priority=10;parse(e,t){switch(t){case"x":return T(k.basicOptionalMinutes,e);case"xx":return T(k.basic,e);case"xxxx":return T(k.basicOptionalSeconds,e);case"xxxxx":return T(k.extendedOptionalSeconds,e);case"xxx":default:return T(k.extended,e)}}set(e,t,n){return t.timestampIsSet?e:g(e,e.getTime()-A(e)-n)}incompatibleTokens=["t","T","X"]}class dr extends f{priority=40;parse(e){return Ne(e)}set(e,t,n){return[g(e,n*1e3),{timestampIsSet:!0}]}incompatibleTokens="*"}class lr extends f{priority=20;parse(e){return Ne(e)}set(e,t,n){return[g(e,n),{timestampIsSet:!0}]}incompatibleTokens="*"}const fr={G:new Wn,y:new Nn,Y:new Cn,R:new En,u:new qn,Q:new Fn,q:new Hn,M:new In,L:new Ln,w:new Rn,I:new An,d:new $n,D:new jn,E:new Un,e:new zn,c:new Vn,i:new Jn,a:new Kn,b:new Sn,B:new er,h:new tr,H:new nr,K:new rr,k:new ar,m:new sr,s:new or,S:new ir,X:new ur,x:new cr,t:new dr,T:new lr},hr=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,mr=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,wr=/^'([^]*?)'?$/,yr=/''/g,gr=/\S/,pr=/[a-zA-Z]/;function br(r,e,t,n){const a=()=>g(n?.in||t,NaN),s=kn(),o=n?.locale??s.locale??Te,i=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,c=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??s.weekStartsOn??s.locale?.options?.weekStartsOn??0;if(!e)return r?a():u(t,n?.in);const d={firstWeekContainsDate:i,weekStartsOn:c,locale:o},m=[new vn(n?.in,t)],M=e.match(mr).map(l=>{const y=l[0];if(y in V){const D=V[y];return D(l,o.formatLong)}return l}).join("").match(hr),Y=[];for(let l of M){!n?.useAdditionalWeekYearTokens&&ve(l)&&Z(l,e,r),!n?.useAdditionalDayOfYearTokens&&_e(l)&&Z(l,e,r);const y=l[0],D=fr[y];if(D){const{incompatibleTokens:ae}=D;if(Array.isArray(ae)){const se=Y.find(oe=>ae.includes(oe.token)||oe.token===y);if(se)throw new RangeError(`The format string mustn't contain \`${se.fullToken}\` and \`${l}\` at the same time`)}else if(D.incompatibleTokens==="*"&&Y.length>0)throw new RangeError(`The format string mustn't contain \`${l}\` and any other token at the same time`);Y.push({token:y,fullToken:l});const $=D.run(r,l,o.match,d);if(!$)return a();m.push($.setter),r=$.rest}else{if(y.match(pr))throw new RangeError("Format string contains an unescaped latin alphabet character `"+y+"`");if(l==="''"?l="'":y==="'"&&(l=xr(l)),r.indexOf(l)===0)r=r.slice(l.length);else return a()}}if(r.length>0&&gr.test(r))return a();const Fe=m.map(l=>l.priority).sort((l,y)=>y-l).filter((l,y,D)=>D.indexOf(l)===y).map(l=>m.filter(y=>y.priority===l).sort((y,D)=>D.subPriority-y.subPriority)).map(l=>l[0]);let N=u(t,n?.in);if(isNaN(+N))return a();const re={};for(const l of Fe){if(!l.validate(N,d))return a();const y=l.set(N,re,d);Array.isArray(y)?(N=y[0],Object.assign(re,y[1])):N=y}return N}function xr(r){return r.match(wr)[1].replace(yr,"'")}function Mr(r,e){const t=u(r,e?.in);return t.setMinutes(0,0,0),t}function Dr(r,e){const t=u(r,e?.in);return t.setSeconds(0,0),t}function kr(r,e){const t=u(r,e?.in);return t.setMilliseconds(0),t}function Tr(r,e){const t=()=>g(e?.in,NaN),n=e?.additionalDigits??2,a=_r(r);let s;if(a.date){const d=vr(a.date,n);s=Wr(d.restDateString,d.year)}if(!s||isNaN(+s))return t();const o=+s;let i=0,c;if(a.time&&(i=Nr(a.time),isNaN(i)))return t();if(a.timezone){if(c=Cr(a.timezone),isNaN(c))return t()}else{const d=new Date(o+i),m=u(0,e?.in);return m.setFullYear(d.getUTCFullYear(),d.getUTCMonth(),d.getUTCDate()),m.setHours(d.getUTCHours(),d.getUTCMinutes(),d.getUTCSeconds(),d.getUTCMilliseconds()),m}return u(o+i+c,e?.in)}const B={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Or=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Pr=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Yr=/^([+-])(\d{2})(?::?(\d{2}))?$/;function _r(r){const e={},t=r.split(B.dateTimeDelimiter);let n;if(t.length>2)return e;if(/:/.test(t[0])?n=t[0]:(e.date=t[0],n=t[1],B.timeZoneDelimiter.test(e.date)&&(e.date=r.split(B.timeZoneDelimiter)[0],n=r.substr(e.date.length,r.length))),n){const a=B.timezone.exec(n);a?(e.time=n.replace(a[1],""),e.timezone=a[1]):e.time=n}return e}function vr(r,e){const t=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),n=r.match(t);if(!n)return{year:NaN,restDateString:""};const a=n[1]?parseInt(n[1]):null,s=n[2]?parseInt(n[2]):null;return{year:s===null?a:s*100,restDateString:r.slice((n[1]||n[2]).length)}}function Wr(r,e){if(e===null)return new Date(NaN);const t=r.match(Or);if(!t)return new Date(NaN);const n=!!t[4],a=H(t[1]),s=H(t[2])-1,o=H(t[3]),i=H(t[4]),c=H(t[5])-1;if(n)return Ir(e,i,c)?Er(e,i,c):new Date(NaN);{const d=new Date(0);return!Fr(e,s,o)||!Hr(e,a)?new Date(NaN):(d.setUTCFullYear(e,s,Math.max(a,o)),d)}}function H(r){return r?parseInt(r):1}function Nr(r){const e=r.match(Pr);if(!e)return NaN;const t=U(e[1]),n=U(e[2]),a=U(e[3]);return Lr(t,n,a)?t*Q+n*L+a*1e3:NaN}function U(r){return r&&parseFloat(r.replace(",","."))||0}function Cr(r){if(r==="Z")return 0;const e=r.match(Yr);if(!e)return 0;const t=e[1]==="+"?-1:1,n=parseInt(e[2]),a=e[3]&&parseInt(e[3])||0;return Qr(n,a)?t*(n*Q+a*L):NaN}function Er(r,e,t){const n=new Date(0);n.setUTCFullYear(r,0,4);const a=n.getUTCDay()||7,s=(e-1)*7+t+1-a;return n.setUTCDate(n.getUTCDate()+s),n}const qr=[31,null,31,30,31,30,31,31,30,31,30,31];function qe(r){return r%400===0||r%4===0&&r%100!==0}function Fr(r,e,t){return e>=0&&e<=11&&t>=1&&t<=(qr[e]||(qe(r)?29:28))}function Hr(r,e){return e>=1&&e<=(qe(r)?366:365)}function Ir(r,e,t){return e>=1&&e<=53&&t>=0&&t<=6}function Lr(r,e,t){return r===24?e===0&&t===0:t>=0&&t<60&&e>=0&&e<60&&r>=0&&r<25}function Qr(r,e){return e>=0&&e<=59}/*!
 * chartjs-adapter-date-fns v3.0.0
 * https://www.chartjs.org
 * (c) 2022 chartjs-adapter-date-fns Contributors
 * Released under the MIT license
 */const Rr={datetime:"MMM d, yyyy, h:mm:ss aaaa",millisecond:"h:mm:ss.SSS aaaa",second:"h:mm:ss aaaa",minute:"h:mm aaaa",hour:"ha",day:"MMM d",week:"PP",month:"MMM yyyy",quarter:"qqq - yyyy",year:"yyyy"};He._date.override({_id:"date-fns",formats:function(){return Rr},parse:function(r,e){if(r===null||typeof r>"u")return null;const t=typeof r;return t==="number"||r instanceof Date?r=u(r):t==="string"&&(typeof e=="string"?r=br(r,e,new Date,this.options):r=Tr(r,this.options)),pe(r)?r.getTime():null},format:function(r,e){return Mn(r,e,this.options)},add:function(r,e,t){switch(t){case"millisecond":return K(r,e);case"second":return ct(r,e);case"minute":return it(r,e);case"hour":return at(r,e);case"day":return G(r,e);case"week":return dt(r,e);case"month":return J(r,e);case"quarter":return ut(r,e);case"year":return lt(r,e);default:return r}},diff:function(r,e,t){switch(t){case"millisecond":return S(r,e);case"second":return bt(r,e);case"minute":return yt(r,e);case"hour":return wt(r,e);case"day":return be(r,e);case"week":return xt(r,e);case"month":return De(r,e);case"quarter":return pt(r,e);case"year":return Mt(r,e);default:return 0}},startOf:function(r,e,t){switch(e){case"second":return kr(r);case"minute":return Dr(r);case"hour":return Mr(r);case"day":return z(r);case"week":return O(r);case"isoWeek":return O(r,{weekStartsOn:+t});case"month":return kt(r);case"quarter":return Dt(r);case"year":return ke(r);default:return r}},endOf:function(r,e){switch(e){case"second":return vt(r);case"minute":return Yt(r);case"hour":return Ot(r);case"day":return xe(r);case"week":return Pt(r);case"month":return Me(r);case"quarter":return _t(r);case"year":return Tt(r);default:return r}}});const Br={__name:"Chart",props:{type:{type:String,required:!0,validator:r=>["line","bar","doughnut","pie"].includes(r)},data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:Number,default:400},responsive:{type:Boolean,default:!0}},setup(r){ie.register(Ie,Le,Qe,Re,Be,Ae,Xe,Ge,$e,je);const e=r,t=ze(null);let n=null;const a={responsive:e.responsive,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(i){let c=i.dataset.label||"";return c&&(c+=": "),i.parsed.y!==null&&(i.parsed.y>=1e3||i.dataset.label?.toLowerCase().includes("revenue")||i.dataset.label?.toLowerCase().includes("cost")||i.dataset.label?.toLowerCase().includes("profit")?c+="₱"+i.parsed.y.toLocaleString("en-US",{minimumFractionDigits:2}):c+=i.parsed.y.toLocaleString()),c}}}},scales:{}};(e.type==="line"||e.type==="bar")&&(a.scales={x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"},callback:function(i){return i>=1e3?"₱"+i.toLocaleString("en-US",{minimumFractionDigits:0}):i}},grid:{color:"#374151",borderColor:"#4B5563"}}});const s=()=>{n&&n.destroy();const i=t.value.getContext("2d"),c={...a,...e.options};n=new ie(i,{type:e.type,data:e.data,options:c})},o=()=>{n&&(n.data=e.data,n.update("none"))};return Ve(async()=>{await Ze(),s()}),Je(()=>{n&&n.destroy()}),ue(()=>e.data,()=>{o()},{deep:!0}),ue(()=>e.options,()=>{s()},{deep:!0}),(i,c)=>(Se(),Ke("div",{class:"relative",style:tt({height:r.height+"px"})},[et("canvas",{ref_key:"chartCanvas",ref:t,class:"w-full h-full"},null,512)],4))}},Gr=Ue(Br,[["__scopeId","data-v-4bbae718"]]);export{Gr as C};
