import{r as d,m as I,x as q,q as M,g as i,o as r,a as p,d as v,h as O,w as g,b as e,t as o,i as x,F as w,y as T,f as j,l as b,c as z,n as G,j as Q,v as U}from"./app-BGh5SFxi.js";import{_ as W}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as Z}from"./CustomerModal-D_puuOms.js";import{_ as J}from"./ConfirmationModal-Bx9j458V.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const K={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},R={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},X={class:"relative flex-1 max-w-md"},Y={class:"flex items-center space-x-2"},ee={class:"p-6 space-y-6"},te={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},se={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},oe={class:"flex items-center justify-between"},re={class:"text-2xl font-bold text-white"},le={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ne={class:"flex items-center justify-between"},ae={class:"text-2xl font-bold text-white"},ie={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},de={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-white"},ce={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},xe={class:"overflow-x-auto"},me={class:"min-w-full divide-y divide-gray-700"},he={class:"bg-gray-900 divide-y divide-gray-700"},pe={class:"px-6 py-4 whitespace-nowrap"},ve={class:"flex items-center"},ge={class:"w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-shadow duration-200"},fe={class:"text-sm font-bold text-white"},ye={class:"text-sm font-semibold text-white group-hover:text-red-300 transition-colors duration-200"},we={class:"text-xs text-gray-400"},be={class:"px-6 py-4 whitespace-nowrap"},ke={class:"text-sm text-gray-300"},_e=["href"],Ce={class:"text-sm text-gray-400"},Me={class:"px-6 py-4 whitespace-nowrap"},je={class:"text-sm text-gray-300"},ze={class:"text-xs text-gray-400"},Be={class:"px-6 py-4 whitespace-nowrap"},He={class:"flex items-center"},Ve={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm"},De={class:"px-6 py-4 whitespace-nowrap text-center"},Le={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 shadow-sm"},Te={class:"px-6 py-4 whitespace-nowrap text-center"},Ae={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm"},Ne={class:"px-6 py-4 whitespace-nowrap text-center"},Se={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm"},$e={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ee={class:"flex items-center justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},Fe=["onClick"],Pe=["onClick"],Ie={key:0},qe={colspan:"7",class:"px-6 py-12 text-center"},Oe={class:"flex flex-col items-center"},Ge={class:"text-lg font-medium text-white mb-2"},Qe={class:"text-gray-400 text-sm mb-4"},Ue={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},We={class:"flex items-center justify-between"},Ze={class:"flex-1 flex justify-between sm:hidden"},Je={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ke={class:"text-sm text-gray-400"},Re={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Xe=["innerHTML"],lt={__name:"Index",props:{customers:Object,filters:Object},setup(n){const u=n,m=d(!1),f=d(!1),k=d(null),c=d(null),h=d(!1),l=d(u.filters?.search||""),y=d(u.filters?.status||"all"),_=d(u.filters?.sort||"name"),C=d(u.filters?.direction||"asc"),B=I(()=>{if(!u.customers?.data)return[];let a=[...u.customers.data];if(l.value){const t=l.value.toLowerCase();a=a.filter(s=>s.full_name?.toLowerCase().includes(t)||s.facebook_link?.toLowerCase().includes(t)||s.phone?.toLowerCase().includes(t))}return a});let H;q(l,a=>{clearTimeout(H),H=setTimeout(()=>{D()},300)});const V=()=>{k.value=null,m.value=!0},A=a=>{k.value=a,m.value=!0},N=a=>{c.value=a,f.value=!0},S=()=>{m.value=!1,M.reload({only:["customers"]})},$=()=>{c.value&&(h.value=!0,M.delete(route("customers.destroy",c.value.id),{onSuccess:()=>{f.value=!1,c.value=null,h.value=!1},onError:()=>{h.value=!1}}))},E=()=>{f.value=!1,c.value=null,h.value=!1},D=()=>{const a={search:l.value||void 0,status:y.value!=="all"?y.value:void 0,sort:_.value!=="name"?_.value:void 0,direction:C.value!=="asc"?C.value:void 0};M.get(route("customers.index"),a,{preserveState:!0,replace:!0})},F=()=>{console.log("Exporting customers...")},L=()=>{l.value="",y.value="all",_.value="name",C.value="asc",D()};return(a,t)=>(r(),i(w,null,[p(v(O),{title:"Customers"}),p(W,null,{header:g(()=>[e("div",K,[t[7]||(t[7]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Customer Management "),e("p",{class:"text-gray-400 text-sm"},"Manage your customer database and relationships")],-1)),e("div",R,[e("div",X,[Q(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>l.value=s),type:"text",placeholder:"Search customers...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[U,l.value]]),t[4]||(t[4]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),l.value?(r(),i("button",{key:0,onClick:t[1]||(t[1]=s=>l.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[3]||(t[3]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):x("",!0)]),e("div",Y,[e("button",{onClick:F,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("span",null,"Export",-1)])),l.value||y.value!=="all"?(r(),i("button",{key:0,onClick:L,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):x("",!0),e("button",{onClick:V,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Customer",-1)]))])])])]),default:g(()=>[e("div",ee,[e("div",te,[e("div",se,[e("div",oe,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-sm text-gray-400"},"Total Customers",-1)),e("p",re,o(n.customers.total||0),1)]),t[9]||(t[9]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1))])]),e("div",le,[e("div",ne,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-sm text-gray-400"},"Active This Month",-1)),e("p",ae,o(Math.floor((n.customers.total||0)*.7)),1)]),t[11]||(t[11]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1))])]),e("div",ie,[e("div",de,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm text-gray-400"},"New This Week",-1)),e("p",ue,o(Math.floor((n.customers.total||0)*.1)),1)]),t[13]||(t[13]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])],-1))])]),t[14]||(t[14]=e("div",{class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},[e("div",{class:"flex items-center justify-between"},[e("div",null,[e("p",{class:"text-sm text-gray-400"},"Avg. Devices"),e("p",{class:"text-2xl font-bold text-white"},"2.3")]),e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])])])],-1))]),e("div",ce,[t[24]||(t[24]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Customer Directory")]),e("div",{class:"flex items-center space-x-2"},[e("button",{class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"})])]),e("button",{class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])])])])],-1)),e("div",xe,[e("table",me,[t[21]||(t[21]=e("thead",{class:"bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Customer"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Contact"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Location"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Devices"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Pending"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"In Progress"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Completed"),e("th",{class:"px-6 py-4 text-right text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",he,[(r(!0),i(w,null,T(B.value,s=>(r(),i("tr",{key:s.id,class:"hover:bg-gray-800 transition-colors duration-200 group"},[e("td",pe,[e("div",ve,[e("div",ge,[e("span",fe,o(s.first_name?.charAt(0))+o(s.last_name?.charAt(0)),1)]),e("div",null,[e("div",ye,o(s.full_name),1),e("div",we,"Customer ID: #"+o(String(s.id).padStart(4,"0")),1)])])]),e("td",be,[e("div",ke,[e("a",{href:s.facebook_link,target:"_blank",class:"text-blue-400 hover:text-blue-300 transition-colors duration-200"}," Facebook Profile ",8,_e)]),e("div",Ce,o(s.phone),1)]),e("td",Me,[e("div",je,o(s.city||"N/A")+", "+o(s.state||"N/A"),1),e("div",ze,o(s.zip_code||"No ZIP"),1)]),e("td",Be,[e("div",He,[e("span",Ve,[t[15]||(t[15]=e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1)),j(" "+o(s.devices_count||0),1)])])]),e("td",De,[e("span",Le,o(s.pending_orders_count||0),1)]),e("td",Te,[e("span",Ae,o(s.in_progress_orders_count||0),1)]),e("td",Ne,[e("span",Se,o(s.completed_orders_count||0),1)]),e("td",$e,[e("div",Ee,[p(v(b),{href:a.route("customers.show",s.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Customer"},{default:g(()=>t[16]||(t[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[16]},1032,["href"]),e("button",{onClick:P=>A(s),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Customer"},t[17]||(t[17]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Fe),e("button",{onClick:P=>N(s),class:"p-2 text-red-400 hover:text-red-300 hover:bg-red-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Delete Customer"},t[18]||(t[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Pe)])])]))),128)),B.value.length===0?(r(),i("tr",Ie,[e("td",qe,[e("div",Oe,[t[20]||(t[20]=e("div",{class:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4"},[e("svg",{class:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1)),e("h3",Ge,o(l.value?"No customers found":"No customers yet"),1),e("p",Qe,o(l.value?`No customers match "${l.value}"`:"Get started by adding your first customer"),1),l.value?(r(),i("button",{key:1,onClick:L,class:"text-red-400 hover:text-red-300 text-sm font-medium"}," Clear search and show all customers → ")):(r(),i("button",{key:0,onClick:V,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[19]||(t[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add your first customer",-1)])))])])])):x("",!0)])])]),n.customers.links?(r(),i("div",Ue,[e("div",We,[e("div",Ze,[n.customers.prev_page_url?(r(),z(v(b),{key:0,href:n.customers.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:g(()=>t[22]||(t[22]=[j(" Previous ",-1)])),_:1,__:[22]},8,["href"])):x("",!0),n.customers.next_page_url?(r(),z(v(b),{key:1,href:n.customers.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:g(()=>t[23]||(t[23]=[j(" Next ",-1)])),_:1,__:[23]},8,["href"])):x("",!0)]),e("div",Je,[e("div",null,[e("p",Ke," Showing "+o(n.customers.from)+" to "+o(n.customers.to)+" of "+o(n.customers.total)+" results ",1)]),e("div",null,[e("nav",Re,[(r(!0),i(w,null,T(n.customers.links,s=>(r(),i(w,{key:s.label},[s.url?(r(),z(v(b),{key:0,href:s.url,class:G(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":s.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!s.active}]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(r(),i("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:s.label},null,8,Xe))],64))),128))])])])])])):x("",!0)])]),p(Z,{show:m.value,customer:k.value,onClose:t[2]||(t[2]=s=>m.value=!1),onSaved:S},null,8,["show","customer"]),p(J,{show:f.value,processing:h.value,title:"Delete Customer",message:`Are you sure you want to delete ${c.value?.full_name}? This action cannot be undone and will also delete all associated devices and repair orders.`,"confirm-text":"Delete Customer","cancel-text":"Cancel",type:"danger",onConfirm:$,onCancel:E},null,8,["show","processing","message"])]),_:1})],64))}};export{lt as default};
