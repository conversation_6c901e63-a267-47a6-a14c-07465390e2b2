import{u as Q,r as w,x as E,g as n,i as p,o as a,b as e,t as u,e as R,j as v,v as y,d as s,I as L,F as B,y as N,k as ce,K as J,M as H,n as C,f as M,m as pe,q as A,a as U,h as ge,w as I,l as z,c as F}from"./app-BGh5SFxi.js";import{_ as me}from"./AuthenticatedLayout-D7wPPUcl.js";import{_ as ve}from"./ConfirmationModal-Bx9j458V.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";const xe={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},ye={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},be={class:"relative inline-block align-bottom bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full border border-gray-700"},fe={class:"bg-gradient-to-r from-gray-800 to-gray-700 px-6 py-4 border-b border-gray-600"},we={class:"flex items-center justify-between"},ke={class:"flex items-center space-x-3"},he={class:"text-lg font-semibold text-white",id:"modal-title"},_e={key:0},Ce={key:0,class:"mt-1 text-sm text-red-400"},$e={key:0,class:"mt-1 text-sm text-red-400"},Se={key:1,class:"mt-1 text-xs text-gray-400"},Me={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},je=["value"],qe={key:0,class:"mt-1 text-sm text-red-400"},Pe={key:0,class:"mt-1 text-sm text-red-400"},Ve=["value"],Ue={key:0,class:"mt-1 text-sm text-red-400"},Be={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Le={class:"relative"},De={key:0,class:"mt-1 text-sm text-red-400"},Te={class:"relative"},Ae={key:0,class:"mt-1 text-sm text-red-400"},Ie={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ne={key:0,class:"mt-1 text-sm text-red-400"},Oe={key:0,class:"mt-1 text-sm text-red-400"},ze={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},Ee={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},He={key:0,class:"mt-1 text-sm text-red-400"},Fe={key:0},Qe={key:0,class:"mt-1 text-sm text-red-400"},Re={key:1},Ke={key:0,class:"mt-1 text-sm text-red-400"},Ge={key:2},Je={key:0,class:"mt-1 text-sm text-red-400"},We={key:0,class:"mt-1 text-sm text-red-400"},Xe={class:"flex items-center"},Ye={class:"flex items-center justify-end space-x-3 pt-6 border-t border-gray-700"},Ze=["disabled"],et={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},tt={__name:"PartModal",props:{show:{type:Boolean,default:!1},part:{type:Object,default:null},devices:{type:Array,default:()=>[]}},emits:["close","saved"],setup(g,{emit:h}){const c=g,_=h,t=Q({part_number:"",name:"",description:"",category:"",device_id:null,compatible_devices:[],cost_price:"",selling_price:"",quantity_in_stock:"",minimum_stock_level:"",supplier:"",is_active:!0,status:"ordered",order_date:"",expected_arrival_date:"",received_date:""}),b=w(!1);E(()=>c.show,l=>{l&&(c.part?(b.value=!0,t.part_number=c.part.part_number||"",t.name=c.part.name||"",t.description=c.part.description||"",t.category=c.part.category||"",t.device_id=c.part.device_id||null,t.compatible_devices=c.part.compatible_devices||[],t.cost_price=c.part.cost_price||"",t.selling_price=c.part.selling_price||"",t.quantity_in_stock=c.part.quantity_in_stock||"",t.minimum_stock_level=c.part.minimum_stock_level||"",t.supplier=c.part.supplier||"",t.is_active=c.part.is_active??!0,t.status=c.part.status||"ordered",t.order_date=c.part.order_date?new Date(c.part.order_date).toISOString().split("T")[0]:"",t.expected_arrival_date=c.part.expected_arrival_date?new Date(c.part.expected_arrival_date).toISOString().split("T")[0]:"",t.received_date=c.part.received_date?new Date(c.part.received_date).toISOString().split("T")[0]:""):(b.value=!1,t.reset()))});const k=()=>{b.value?t.put(route("parts.update",c.part.id),{onSuccess:()=>{window.toast&&window.toast.success("Part updated successfully!"),_("saved"),f()},onError:l=>{console.error("Part update failed:",l),window.toast&&window.toast.error("Failed to update part. Please try again.")}}):t.post(route("parts.store"),{onSuccess:()=>{window.toast&&window.toast.success("Part created successfully!"),_("saved"),f()},onError:l=>{console.error("Part creation failed:",l),window.toast&&window.toast.error("Failed to create part. Please try again.")}})},f=()=>{t.reset(),t.clearErrors(),_("close")},x=["Screen Components","Battery","Charging Components","Audio Components","Camera Components","Internal Components","External Components","Tools & Equipment","Adhesives & Tapes","Other"];return(l,r)=>g.show?(a(),n("div",xe,[e("div",ye,[e("div",{class:"fixed inset-0 bg-black bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:f}),e("div",be,[e("div",fe,[e("div",we,[e("div",ke,[r[15]||(r[15]=e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])],-1)),e("h3",he,u(b.value?"Edit Part":"Add New Part"),1)]),e("button",{onClick:f,class:"text-gray-400 hover:text-white transition-colors duration-200"},r[16]||(r[16]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("form",{onSubmit:R(k,["prevent"]),class:"p-6 space-y-6"},[b.value?(a(),n("div",_e,[r[17]||(r[17]=e("label",{for:"part_number",class:"block text-sm font-medium text-gray-300 mb-2"}," Part Number * ",-1)),v(e("input",{id:"part_number","onUpdate:modelValue":r[0]||(r[0]=d=>s(t).part_number=d),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"PN-2024-000001",required:""},null,512),[[y,s(t).part_number]]),s(t).errors.part_number?(a(),n("div",Ce,u(s(t).errors.part_number),1)):p("",!0)])):p("",!0),e("div",null,[r[18]||(r[18]=e("label",{for:"name",class:"block text-sm font-medium text-gray-300 mb-2"}," Part Name * ",-1)),v(e("input",{id:"name","onUpdate:modelValue":r[1]||(r[1]=d=>s(t).name=d),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"iPhone 12 Screen",required:""},null,512),[[y,s(t).name]]),s(t).errors.name?(a(),n("div",$e,u(s(t).errors.name),1)):p("",!0),b.value?p("",!0):(a(),n("p",Se,"Part number will be auto-generated"))]),e("div",Me,[e("div",null,[r[20]||(r[20]=e("label",{for:"category",class:"block text-sm font-medium text-gray-300 mb-2"}," Category * ",-1)),v(e("select",{id:"category","onUpdate:modelValue":r[2]||(r[2]=d=>s(t).category=d),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[r[19]||(r[19]=e("option",{value:"",class:"bg-gray-700 text-white"},"Select category...",-1)),(a(),n(B,null,N(x,d=>e("option",{key:d,value:d,class:"bg-gray-700 text-white"},u(d),9,je)),64))],512),[[L,s(t).category]]),s(t).errors.category?(a(),n("div",qe,u(s(t).errors.category),1)):p("",!0)]),e("div",null,[r[21]||(r[21]=e("label",{for:"supplier",class:"block text-sm font-medium text-gray-300 mb-2"}," Supplier ",-1)),v(e("input",{id:"supplier","onUpdate:modelValue":r[3]||(r[3]=d=>s(t).supplier=d),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Supplier name"},null,512),[[y,s(t).supplier]]),s(t).errors.supplier?(a(),n("div",Pe,u(s(t).errors.supplier),1)):p("",!0)])]),e("div",null,[r[23]||(r[23]=e("label",{for:"device_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Associated Device (Optional) ",-1)),v(e("select",{id:"device_id","onUpdate:modelValue":r[4]||(r[4]=d=>s(t).device_id=d),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},[r[22]||(r[22]=e("option",{value:"",class:"bg-gray-700 text-white"},"No specific device",-1)),(a(!0),n(B,null,N(g.devices,d=>(a(),n("option",{key:d.id,value:d.id,class:"bg-gray-700 text-white"},u(d.label),9,Ve))),128))],512),[[L,s(t).device_id]]),s(t).errors.device_id?(a(),n("div",Ue,u(s(t).errors.device_id),1)):p("",!0),r[24]||(r[24]=e("p",{class:"mt-1 text-xs text-gray-400"}," Link this part to a specific registered device. This will help auto-suggest parts when creating repair orders for this device. ",-1))]),e("div",Be,[e("div",null,[r[26]||(r[26]=e("label",{for:"cost_price",class:"block text-sm font-medium text-gray-300 mb-2"}," Cost Price * ",-1)),e("div",Le,[r[25]||(r[25]=e("span",{class:"absolute left-3 top-2.5 text-gray-400"},"₱",-1)),v(e("input",{id:"cost_price","onUpdate:modelValue":r[5]||(r[5]=d=>s(t).cost_price=d),type:"number",step:"0.01",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200 pl-8",placeholder:"500.00",required:""},null,512),[[y,s(t).cost_price]])]),s(t).errors.cost_price?(a(),n("div",De,u(s(t).errors.cost_price),1)):p("",!0)]),e("div",null,[r[28]||(r[28]=e("label",{for:"selling_price",class:"block text-sm font-medium text-gray-300 mb-2"}," Selling Price * ",-1)),e("div",Te,[r[27]||(r[27]=e("span",{class:"absolute left-3 top-2.5 text-gray-400"},"₱",-1)),v(e("input",{id:"selling_price","onUpdate:modelValue":r[6]||(r[6]=d=>s(t).selling_price=d),type:"number",step:"0.01",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200 pl-8",placeholder:"750.00",required:""},null,512),[[y,s(t).selling_price]])]),s(t).errors.selling_price?(a(),n("div",Ae,u(s(t).errors.selling_price),1)):p("",!0)])]),e("div",Ie,[e("div",null,[r[29]||(r[29]=e("label",{for:"quantity_in_stock",class:"block text-sm font-medium text-gray-300 mb-2"}," Quantity in Stock * ",-1)),v(e("input",{id:"quantity_in_stock","onUpdate:modelValue":r[7]||(r[7]=d=>s(t).quantity_in_stock=d),type:"number",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"10",required:""},null,512),[[y,s(t).quantity_in_stock]]),s(t).errors.quantity_in_stock?(a(),n("div",Ne,u(s(t).errors.quantity_in_stock),1)):p("",!0)]),e("div",null,[r[30]||(r[30]=e("label",{for:"minimum_stock_level",class:"block text-sm font-medium text-gray-300 mb-2"}," Minimum Stock Level * ",-1)),v(e("input",{id:"minimum_stock_level","onUpdate:modelValue":r[8]||(r[8]=d=>s(t).minimum_stock_level=d),type:"number",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"5",required:""},null,512),[[y,s(t).minimum_stock_level]]),s(t).errors.minimum_stock_level?(a(),n("div",Oe,u(s(t).errors.minimum_stock_level),1)):p("",!0)])]),e("div",ze,[r[36]||(r[36]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Status Information",-1)),e("div",Ee,[e("div",null,[r[32]||(r[32]=e("label",{for:"status",class:"block text-sm font-medium text-gray-300 mb-2"}," Status * ",-1)),v(e("select",{id:"status","onUpdate:modelValue":r[9]||(r[9]=d=>s(t).status=d),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},r[31]||(r[31]=[e("option",{value:"ordered",class:"bg-gray-700 text-white",selected:""},"Ordered",-1),e("option",{value:"in_transit",class:"bg-gray-700 text-white"},"In Transit",-1),e("option",{value:"in_stock",class:"bg-gray-700 text-white"},"In Stock",-1)]),512),[[L,s(t).status]]),s(t).errors.status?(a(),n("div",He,u(s(t).errors.status),1)):p("",!0)]),s(t).status==="ordered"?(a(),n("div",Fe,[r[33]||(r[33]=e("label",{for:"order_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Order Date ",-1)),v(e("input",{id:"order_date","onUpdate:modelValue":r[10]||(r[10]=d=>s(t).order_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},null,512),[[y,s(t).order_date]]),s(t).errors.order_date?(a(),n("div",Qe,u(s(t).errors.order_date),1)):p("",!0)])):p("",!0),s(t).status==="ordered"||s(t).status==="in_transit"?(a(),n("div",Re,[r[34]||(r[34]=e("label",{for:"expected_arrival_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Expected Arrival Date ",-1)),v(e("input",{id:"expected_arrival_date","onUpdate:modelValue":r[11]||(r[11]=d=>s(t).expected_arrival_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},null,512),[[y,s(t).expected_arrival_date]]),s(t).errors.expected_arrival_date?(a(),n("div",Ke,u(s(t).errors.expected_arrival_date),1)):p("",!0)])):p("",!0),s(t).status==="in_stock"?(a(),n("div",Ge,[r[35]||(r[35]=e("label",{for:"received_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Received Date ",-1)),v(e("input",{id:"received_date","onUpdate:modelValue":r[12]||(r[12]=d=>s(t).received_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200"},null,512),[[y,s(t).received_date]]),s(t).errors.received_date?(a(),n("div",Je,u(s(t).errors.received_date),1)):p("",!0)])):p("",!0)])]),e("div",null,[r[37]||(r[37]=e("label",{for:"description",class:"block text-sm font-medium text-gray-300 mb-2"}," Description ",-1)),v(e("textarea",{id:"description","onUpdate:modelValue":r[13]||(r[13]=d=>s(t).description=d),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Part description and specifications..."},null,512),[[y,s(t).description]]),s(t).errors.description?(a(),n("div",We,u(s(t).errors.description),1)):p("",!0)]),e("div",Xe,[v(e("input",{id:"is_active","onUpdate:modelValue":r[14]||(r[14]=d=>s(t).is_active=d),type:"checkbox",class:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-600 rounded bg-gray-700"},null,512),[[ce,s(t).is_active]]),r[38]||(r[38]=e("label",{for:"is_active",class:"ml-2 block text-sm text-gray-300"}," Active (available for use) ",-1))]),e("div",Ye,[e("button",{type:"button",onClick:f,class:"px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:s(t).processing,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"},[s(t).processing?(a(),n("svg",et,r[39]||(r[39]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):p("",!0),e("span",null,u(b.value?"Update Part":"Create Part"),1)],8,Ze)])],32)])])])):p("",!0)}},rt={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},st={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ot={class:"relative inline-block align-bottom bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-700"},lt={key:0,class:"px-6 py-4 bg-gray-800 border-b border-gray-700"},it={class:"flex items-center justify-between"},at={class:"text-lg font-medium text-white"},nt={class:"text-sm text-gray-400"},dt={class:"text-right"},ut={class:"text-2xl font-bold text-white"},ct={class:"grid grid-cols-3 gap-3"},pt={class:"relative"},gt={class:"relative"},mt={class:"relative"},vt=["placeholder"],xt={key:0,class:"mt-1 text-sm text-red-400"},yt={key:0,class:"p-4 bg-gray-800 rounded-lg border border-gray-700"},bt={class:"flex items-center justify-between"},ft={class:"text-lg font-semibold text-white"},wt={class:"flex items-center justify-between mt-2"},kt={key:0,class:"mt-1 text-sm text-red-400"},ht={class:"flex items-center justify-end space-x-3 pt-6 border-t border-gray-700"},_t=["disabled"],Ct={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},$t={__name:"StockUpdateModal",props:{show:{type:Boolean,default:!1},part:{type:Object,default:null}},emits:["close","saved"],setup(g,{emit:h}){const c=g,_=h,t=Q({quantity:"",type:"add",notes:""});E(()=>c.show,x=>{x&&t.reset()});const b=()=>{c.part&&t.patch(route("parts.update-stock",c.part.id),{onSuccess:()=>{window.toast&&window.toast.success("Stock updated successfully!"),_("saved"),k()},onError:x=>{console.error("Stock update failed:",x),window.toast&&window.toast.error("Failed to update stock. Please try again.")}})},k=()=>{t.reset(),t.clearErrors(),_("close")},f=()=>{if(!c.part||!t.quantity)return c.part?.quantity_in_stock||0;const x=c.part.quantity_in_stock,l=parseInt(t.quantity);switch(t.type){case"add":return x+l;case"subtract":return Math.max(0,x-l);case"set":return l;default:return x}};return(x,l)=>g.show?(a(),n("div",rt,[e("div",st,[e("div",{class:"fixed inset-0 bg-black bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:k}),e("div",ot,[e("div",{class:"bg-gradient-to-r from-gray-800 to-gray-700 px-6 py-4 border-b border-gray-600"},[e("div",{class:"flex items-center justify-between"},[l[6]||(l[6]=J('<div class="flex items-center space-x-3"><div class="p-2 bg-green-600 rounded-lg"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12l2 2 4-4"></path></svg></div><h3 class="text-lg font-semibold text-white" id="modal-title"> Update Stock </h3></div>',1)),e("button",{onClick:k,class:"text-gray-400 hover:text-white transition-colors duration-200"},l[5]||(l[5]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),g.part?(a(),n("div",lt,[e("div",it,[e("div",null,[e("h4",at,u(g.part.name),1),e("p",nt,u(g.part.part_number),1)]),e("div",dt,[l[7]||(l[7]=e("p",{class:"text-sm text-gray-400"},"Current Stock",-1)),e("p",ut,u(g.part.quantity_in_stock),1)])])])):p("",!0),e("form",{onSubmit:R(b,["prevent"]),class:"p-6 space-y-6"},[e("div",null,[l[11]||(l[11]=e("label",{class:"block text-sm font-medium text-gray-300 mb-3"}," Update Type ",-1)),e("div",ct,[e("label",pt,[v(e("input",{"onUpdate:modelValue":l[0]||(l[0]=r=>s(t).type=r),type:"radio",value:"add",class:"sr-only"},null,512),[[H,s(t).type]]),e("div",{class:C(["flex items-center justify-center p-3 border rounded-lg cursor-pointer transition-colors duration-200",s(t).type==="add"?"border-green-500 bg-green-500 bg-opacity-20 text-green-400":"border-gray-600 text-gray-400 hover:border-gray-500"])},l[8]||(l[8]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),M(" Add ",-1)]),2)]),e("label",gt,[v(e("input",{"onUpdate:modelValue":l[1]||(l[1]=r=>s(t).type=r),type:"radio",value:"subtract",class:"sr-only"},null,512),[[H,s(t).type]]),e("div",{class:C(["flex items-center justify-center p-3 border rounded-lg cursor-pointer transition-colors duration-200",s(t).type==="subtract"?"border-red-500 bg-red-500 bg-opacity-20 text-red-400":"border-gray-600 text-gray-400 hover:border-gray-500"])},l[9]||(l[9]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 12H6"})],-1),M(" Remove ",-1)]),2)]),e("label",mt,[v(e("input",{"onUpdate:modelValue":l[2]||(l[2]=r=>s(t).type=r),type:"radio",value:"set",class:"sr-only"},null,512),[[H,s(t).type]]),e("div",{class:C(["flex items-center justify-center p-3 border rounded-lg cursor-pointer transition-colors duration-200",s(t).type==="set"?"border-blue-500 bg-blue-500 bg-opacity-20 text-blue-400":"border-gray-600 text-gray-400 hover:border-gray-500"])},l[10]||(l[10]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),M(" Set ",-1)]),2)])])]),e("div",null,[l[12]||(l[12]=e("label",{for:"quantity",class:"block text-sm font-medium text-gray-300 mb-2"}," Quantity * ",-1)),v(e("input",{id:"quantity","onUpdate:modelValue":l[3]||(l[3]=r=>s(t).quantity=r),type:"number",min:"0",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-green-500 focus:ring-green-500 transition-colors duration-200",placeholder:s(t).type==="set"?"New total quantity":"Quantity to "+s(t).type,required:""},null,8,vt),[[y,s(t).quantity]]),s(t).errors.quantity?(a(),n("div",xt,u(s(t).errors.quantity),1)):p("",!0)]),s(t).quantity?(a(),n("div",yt,[e("div",bt,[l[13]||(l[13]=e("span",{class:"text-sm text-gray-400"},"New stock level:",-1)),e("span",ft,u(f()),1)]),e("div",wt,[l[14]||(l[14]=e("span",{class:"text-sm text-gray-400"},"Change:",-1)),e("span",{class:C(["text-sm font-medium",f()>g.part?.quantity_in_stock?"text-green-400":f()<g.part?.quantity_in_stock?"text-red-400":"text-gray-400"])},u(f()>g.part?.quantity_in_stock?"+":"")+u(f()-(g.part?.quantity_in_stock||0)),3)])])):p("",!0),e("div",null,[l[15]||(l[15]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-300 mb-2"}," Notes (Optional) ",-1)),v(e("textarea",{id:"notes","onUpdate:modelValue":l[4]||(l[4]=r=>s(t).notes=r),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-green-500 focus:ring-green-500 transition-colors duration-200",placeholder:"Reason for stock update..."},null,512),[[y,s(t).notes]]),s(t).errors.notes?(a(),n("div",kt,u(s(t).errors.notes),1)):p("",!0)]),e("div",ht,[e("button",{type:"button",onClick:k,class:"px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:s(t).processing,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"},[s(t).processing?(a(),n("svg",Ct,l[16]||(l[16]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):p("",!0),l[17]||(l[17]=e("span",null,"Update Stock",-1))],8,_t)])],32)])])])):p("",!0)}},St={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Mt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},jt={class:"relative inline-block align-bottom bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-700"},qt={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},Pt={class:"text-white font-medium mb-2"},Vt={class:"text-gray-400 text-sm"},Ut={class:"mt-2"},Bt=["disabled"],Lt=["disabled"],Dt=["disabled"],Tt={key:0,class:"mt-1 text-sm text-red-400"},At={key:0},It={key:0,class:"mt-1 text-sm text-red-400"},Nt={key:1},Ot={key:0,class:"mt-1 text-sm text-red-400"},zt={key:2},Et={key:0,class:"mt-1 text-sm text-red-400"},Ht={class:"flex items-center justify-end space-x-3 pt-6 border-t border-gray-700"},Ft=["disabled"],Qt={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Rt={__name:"PartStatusModal",props:{show:{type:Boolean,default:!1},part:{type:Object,default:null}},emits:["close","saved"],setup(g,{emit:h}){const c=g,_=h,t=Q({status:"",order_date:"",expected_arrival_date:"",received_date:""});E(()=>c.show,l=>{l&&c.part&&(t.status=c.part.status||"ordered",t.order_date=c.part.order_date?new Date(c.part.order_date).toISOString().split("T")[0]:"",t.expected_arrival_date=c.part.expected_arrival_date?new Date(c.part.expected_arrival_date).toISOString().split("T")[0]:"",t.received_date=c.part.received_date?new Date(c.part.received_date).toISOString().split("T")[0]:"")});const b=()=>{t.patch(route("parts.update-status",c.part.id),{onSuccess:()=>{window.toast&&window.toast.success("Part status updated successfully!"),_("saved"),k()},onError:l=>{console.error("Part status update failed:",l),window.toast&&window.toast.error("Failed to update part status. Please try again.")}})},k=()=>{t.reset(),t.clearErrors(),_("close")},f=l=>{switch(l){case"ordered":return"bg-yellow-900 text-yellow-300 border-yellow-600";case"in_transit":return"bg-blue-900 text-blue-300 border-blue-600";case"in_stock":return"bg-green-900 text-green-300 border-green-600";default:return"bg-gray-900 text-gray-300 border-gray-600"}},x=l=>c.part&&{ordered:["in_transit","in_stock"],in_transit:["in_stock"],in_stock:["ordered"]}[c.part.status]?.includes(l)||!1;return(l,r)=>g.show?(a(),n("div",St,[e("div",Mt,[e("div",{class:"fixed inset-0 bg-black bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:k}),e("div",jt,[e("div",{class:"bg-gradient-to-r from-gray-800 to-gray-700 px-6 py-4 border-b border-gray-600"},[e("div",{class:"flex items-center justify-between"},[r[5]||(r[5]=J('<div class="flex items-center space-x-3"><div class="p-2 bg-blue-600 rounded-lg"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div><h3 class="text-lg font-semibold text-white" id="modal-title"> Update Part Status </h3></div>',1)),e("button",{onClick:k,class:"text-gray-400 hover:text-white transition-colors duration-200"},r[4]||(r[4]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("form",{onSubmit:R(b,["prevent"]),class:"p-6 space-y-6"},[e("div",qt,[e("h4",Pt,u(g.part?.name),1),e("p",Vt,"Part Number: "+u(g.part?.part_number),1),e("div",Ut,[e("span",{class:C(["text-xs px-2 py-1 rounded border",f(g.part?.status)])}," Current: "+u(g.part?.status?.replace("_"," ").toUpperCase()),3)])]),e("div",null,[r[6]||(r[6]=e("label",{for:"status",class:"block text-sm font-medium text-gray-300 mb-2"}," New Status * ",-1)),v(e("select",{id:"status","onUpdate:modelValue":r[0]||(r[0]=d=>s(t).status=d),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200",required:""},[e("option",{value:"ordered",disabled:!x("ordered"),class:"bg-gray-700 text-white"},"Ordered",8,Bt),e("option",{value:"in_transit",disabled:!x("in_transit"),class:"bg-gray-700 text-white"},"In Transit",8,Lt),e("option",{value:"in_stock",disabled:!x("in_stock"),class:"bg-gray-700 text-white"},"In Stock",8,Dt)],512),[[L,s(t).status]]),s(t).errors.status?(a(),n("div",Tt,u(s(t).errors.status),1)):p("",!0)]),s(t).status==="ordered"?(a(),n("div",At,[r[7]||(r[7]=e("label",{for:"order_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Order Date ",-1)),v(e("input",{id:"order_date","onUpdate:modelValue":r[1]||(r[1]=d=>s(t).order_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200"},null,512),[[y,s(t).order_date]]),s(t).errors.order_date?(a(),n("div",It,u(s(t).errors.order_date),1)):p("",!0)])):p("",!0),s(t).status==="ordered"||s(t).status==="in_transit"?(a(),n("div",Nt,[r[8]||(r[8]=e("label",{for:"expected_arrival_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Expected Arrival Date ",-1)),v(e("input",{id:"expected_arrival_date","onUpdate:modelValue":r[2]||(r[2]=d=>s(t).expected_arrival_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200"},null,512),[[y,s(t).expected_arrival_date]]),s(t).errors.expected_arrival_date?(a(),n("div",Ot,u(s(t).errors.expected_arrival_date),1)):p("",!0)])):p("",!0),s(t).status==="in_stock"?(a(),n("div",zt,[r[9]||(r[9]=e("label",{for:"received_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Received Date ",-1)),v(e("input",{id:"received_date","onUpdate:modelValue":r[3]||(r[3]=d=>s(t).received_date=d),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200"},null,512),[[y,s(t).received_date]]),s(t).errors.received_date?(a(),n("div",Et,u(s(t).errors.received_date),1)):p("",!0)])):p("",!0),e("div",Ht,[e("button",{type:"button",onClick:k,class:"px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:s(t).processing,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"},[s(t).processing?(a(),n("svg",Qt,r[10]||(r[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):p("",!0),r[11]||(r[11]=e("span",null,"Update Status",-1))],8,Ft)])],32)])])])):p("",!0)}},Kt={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},Gt={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},Jt={class:"relative flex-1 max-w-md"},Wt={class:"flex items-center space-x-2"},Xt=["value"],Yt={class:"flex items-center space-x-2"},Zt={class:"p-6 space-y-6"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},tr={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},rr={class:"flex items-center justify-between"},sr={class:"text-2xl font-bold text-white"},or={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},lr={class:"flex items-center justify-between"},ir={class:"text-2xl font-bold text-white"},ar={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},nr={class:"flex items-center justify-between"},dr={class:"text-2xl font-bold text-white"},ur={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},cr={class:"flex items-center justify-between"},pr={class:"text-2xl font-bold text-white"},gr={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},mr={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},vr={class:"flex items-center justify-between"},xr={class:"text-sm text-gray-400"},yr={class:"overflow-x-auto"},br={class:"min-w-full divide-y divide-gray-700"},fr={class:"bg-gray-800"},wr={class:"bg-gray-900 divide-y divide-gray-700"},kr={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},hr={class:"px-6 py-4 whitespace-nowrap"},_r={class:"text-sm text-white font-medium"},Cr={class:"text-sm text-gray-400"},$r={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},Sr={class:"px-6 py-4 whitespace-nowrap"},Mr={key:0,class:"text-sm"},jr={class:"text-white font-medium"},qr={class:"text-gray-400"},Pr={key:1,class:"text-sm text-gray-500 italic"},Vr={class:"px-6 py-4 whitespace-nowrap"},Ur={class:"flex items-center space-x-2"},Br={class:"text-sm font-medium text-white"},Lr={class:"text-xs text-gray-400"},Dr={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},Tr={class:"px-6 py-4 whitespace-nowrap"},Ar={class:"flex flex-col space-y-1"},Ir={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Nr={class:"flex items-center space-x-1"},Or=["onClick"],zr=["onClick"],Er=["onClick"],Hr=["onClick"],Fr={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},Qr={class:"flex items-center justify-between"},Rr={class:"flex-1 flex justify-between sm:hidden"},Kr={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Gr={class:"text-sm text-gray-400"},Jr={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Wr=["innerHTML"],rs={__name:"Index",props:{parts:Object,categories:Array,devices:Array,filters:Object},setup(g){const h=g,c=w(!1),_=w(!1),t=w(!1),b=w(!1),k=w(null),f=w(null),x=w(null),l=w(null),r=w(!1),d=w(h.filters?.search||""),j=w(h.filters?.category||"all"),$=w(h.filters?.stock_status||"all"),S=w(h.filters?.status||"all"),P=w(h.filters?.sort||"name"),q=w(h.filters?.direction||"asc");pe(()=>!d.value&&j.value==="all"&&$.value==="all"&&S.value==="all"?h.parts.data:h.parts.data?.filter(m=>{const o=d.value.toLowerCase(),i=!o||m.name?.toLowerCase().includes(o)||m.part_number?.toLowerCase().includes(o)||m.description?.toLowerCase().includes(o)||m.supplier?.toLowerCase().includes(o),V=j.value==="all"||m.category===j.value,ue=S.value==="all"||S.value==="active"&&m.is_active||S.value==="inactive"&&!m.is_active;let O=!0;return $.value==="low_stock"?O=m.quantity_in_stock<=m.minimum_stock_level:$.value==="out_of_stock"?O=m.quantity_in_stock===0:$.value==="in_stock"&&(O=m.quantity_in_stock>0),i&&V&&ue&&O})||[]);let K;E(d,m=>{clearTimeout(K),K=setTimeout(()=>{D()},300)});const W=()=>{k.value=null,c.value=!0},X=m=>{k.value=m,c.value=!0},Y=m=>{f.value=m,_.value=!0},Z=m=>{x.value=m,t.value=!0},ee=m=>{l.value=m,b.value=!0},te=()=>{c.value=!1,A.reload({only:["parts"]})},re=()=>{_.value=!1,A.reload({only:["parts"]})},se=()=>{t.value=!1,A.reload({only:["parts"]})},oe=()=>{l.value&&(r.value=!0,A.delete(route("parts.destroy",l.value.id),{onSuccess:()=>{b.value=!1,l.value=null,r.value=!1},onError:()=>{r.value=!1}}))},le=()=>{b.value=!1,l.value=null,r.value=!1},D=()=>{const m={search:d.value||void 0,category:j.value!=="all"?j.value:void 0,stock_status:$.value!=="all"?$.value:void 0,status:S.value!=="all"?S.value:void 0,sort:P.value,direction:q.value};A.get(route("parts.index"),m,{preserveState:!0,replace:!0})},ie=()=>{d.value="",j.value="all",$.value="all",S.value="all",D()},T=m=>{P.value===m?q.value=q.value==="asc"?"desc":"asc":(P.value=m,q.value="asc"),D()},ae=m=>{switch(m){case"ordered":return"bg-yellow-900 text-yellow-300 border-yellow-600";case"in_transit":return"bg-blue-900 text-blue-300 border-blue-600";case"in_stock":return"bg-green-900 text-green-300 border-green-600";default:return"bg-gray-900 text-gray-300 border-gray-600"}},ne=m=>m.quantity_in_stock===0?"bg-red-100 text-red-800 border-red-200":m.quantity_in_stock<=m.minimum_stock_level?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-green-100 text-green-800 border-green-200",de=m=>m.quantity_in_stock===0?"Out of Stock":m.quantity_in_stock<=m.minimum_stock_level?"Low Stock":"In Stock",G=m=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(m||0);return(m,o)=>(a(),n(B,null,[U(s(ge),{title:"Parts Management"}),U(me,null,{header:I(()=>[e("div",Kt,[o[19]||(o[19]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Parts Management "),e("p",{class:"text-gray-400 text-sm"},"Manage inventory and spare parts")],-1)),e("div",Gt,[e("div",Jt,[v(e("input",{"onUpdate:modelValue":o[0]||(o[0]=i=>d.value=i),type:"text",placeholder:"Search parts...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[y,d.value]]),o[14]||(o[14]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),d.value?(a(),n("button",{key:0,onClick:o[1]||(o[1]=i=>d.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},o[13]||(o[13]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):p("",!0)]),e("div",Wt,[v(e("select",{"onUpdate:modelValue":o[2]||(o[2]=i=>j.value=i),onChange:D,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[o[15]||(o[15]=e("option",{value:"all"},"All Categories",-1)),(a(!0),n(B,null,N(g.categories,i=>(a(),n("option",{key:i,value:i},u(i),9,Xt))),128))],544),[[L,j.value]]),v(e("select",{"onUpdate:modelValue":o[3]||(o[3]=i=>$.value=i),onChange:D,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},o[16]||(o[16]=[e("option",{value:"all"},"All Stock",-1),e("option",{value:"in_stock"},"In Stock",-1),e("option",{value:"low_stock"},"Low Stock",-1),e("option",{value:"out_of_stock"},"Out of Stock",-1)]),544),[[L,$.value]]),v(e("select",{"onUpdate:modelValue":o[4]||(o[4]=i=>S.value=i),onChange:D,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},o[17]||(o[17]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1)]),544),[[L,S.value]])]),e("div",Yt,[d.value||j.value!=="all"||$.value!=="all"||S.value!=="all"?(a(),n("button",{key:0,onClick:ie,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):p("",!0),e("button",{onClick:W,class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},o[18]||(o[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Part",-1)]))])])])]),default:I(()=>[e("div",Zt,[e("div",er,[e("div",tr,[e("div",rr,[e("div",null,[o[20]||(o[20]=e("p",{class:"text-sm text-gray-400"},"Total Parts",-1)),e("p",sr,u(g.parts.total||0),1)]),o[21]||(o[21]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])],-1))])]),e("div",or,[e("div",lr,[e("div",null,[o[22]||(o[22]=e("p",{class:"text-sm text-gray-400"},"Low Stock",-1)),e("p",ir,u(g.parts.data?.filter(i=>i.quantity_in_stock<=i.minimum_stock_level).length||0),1)]),o[23]||(o[23]=e("div",{class:"p-2 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1))])]),e("div",ar,[e("div",nr,[e("div",null,[o[24]||(o[24]=e("p",{class:"text-sm text-gray-400"},"Out of Stock",-1)),e("p",dr,u(g.parts.data?.filter(i=>i.quantity_in_stock===0).length||0),1)]),o[25]||(o[25]=e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"})])],-1))])]),e("div",ur,[e("div",cr,[e("div",null,[o[26]||(o[26]=e("p",{class:"text-sm text-gray-400"},"Total Value",-1)),e("p",pr,u(G(g.parts.data?.reduce((i,V)=>i+V.selling_price*V.quantity_in_stock,0)||0)),1)]),o[27]||(o[27]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("span",{class:"text-white text-xl font-bold"},"₱")],-1))])])]),e("div",gr,[e("div",mr,[e("div",vr,[o[28]||(o[28]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Parts Inventory")],-1)),e("span",xr,u(g.parts.data?.length||0)+" parts",1)])]),e("div",yr,[e("table",br,[e("thead",fr,[e("tr",null,[e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white",onClick:o[5]||(o[5]=i=>T("part_number"))},[o[30]||(o[30]=M(" Part Number ",-1)),P.value==="part_number"?(a(),n("svg",{key:0,class:C(["inline w-4 h-4 ml-1",{"transform rotate-180":q.value==="desc"}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o[29]||(o[29]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]),2)):p("",!0)]),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white",onClick:o[6]||(o[6]=i=>T("name"))},[o[32]||(o[32]=M(" Name ",-1)),P.value==="name"?(a(),n("svg",{key:0,class:C(["inline w-4 h-4 ml-1",{"transform rotate-180":q.value==="desc"}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o[31]||(o[31]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]),2)):p("",!0)]),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white",onClick:o[7]||(o[7]=i=>T("category"))},[o[34]||(o[34]=M(" Category ",-1)),P.value==="category"?(a(),n("svg",{key:0,class:C(["inline w-4 h-4 ml-1",{"transform rotate-180":q.value==="desc"}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o[33]||(o[33]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]),2)):p("",!0)]),o[39]||(o[39]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Associated Device ",-1)),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white",onClick:o[8]||(o[8]=i=>T("quantity_in_stock"))},[o[36]||(o[36]=M(" Stock ",-1)),P.value==="quantity_in_stock"?(a(),n("svg",{key:0,class:C(["inline w-4 h-4 ml-1",{"transform rotate-180":q.value==="desc"}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o[35]||(o[35]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]),2)):p("",!0)]),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white",onClick:o[9]||(o[9]=i=>T("selling_price"))},[o[38]||(o[38]=M(" Price ",-1)),P.value==="selling_price"?(a(),n("svg",{key:0,class:C(["inline w-4 h-4 ml-1",{"transform rotate-180":q.value==="desc"}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o[37]||(o[37]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]),2)):p("",!0)]),o[40]||(o[40]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Status ",-1)),o[41]||(o[41]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Actions ",-1))])]),e("tbody",wr,[(a(!0),n(B,null,N(g.parts.data,i=>(a(),n("tr",{key:i.id,class:"hover:bg-gray-800 transition-colors duration-200"},[e("td",kr,u(i.part_number),1),e("td",hr,[e("div",_r,u(i.name),1),e("div",Cr,u(i.supplier),1)]),e("td",$r,u(i.category),1),e("td",Sr,[i.device?(a(),n("div",Mr,[e("div",jr,u(i.device.customer.first_name)+" "+u(i.device.customer.last_name),1),e("div",qr,u(i.device.brand)+" "+u(i.device.model),1)])):(a(),n("div",Pr," No specific device "))]),e("td",Vr,[e("div",Ur,[e("span",Br,u(i.quantity_in_stock),1),e("span",{class:C(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",ne(i)])},u(de(i)),3)]),e("div",Lr,"Min: "+u(i.minimum_stock_level),1)]),e("td",Dr,u(G(i.selling_price)),1),e("td",Tr,[e("div",Ar,[e("span",{class:C(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",ae(i.status)])},u(i.status?.replace("_"," ").toUpperCase()||"IN STOCK"),3),e("span",{class:C(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",i.is_active?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200"])},u(i.is_active?"Active":"Inactive"),3)])]),e("td",Ir,[e("div",Nr,[U(s(z),{href:m.route("parts.show",i.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Details"},{default:I(()=>o[42]||(o[42]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[42]},1032,["href"]),e("button",{onClick:V=>X(i),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Part"},o[43]||(o[43]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Or),e("button",{onClick:V=>Y(i),class:"p-2 text-green-400 hover:text-green-300 hover:bg-green-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Update Stock"},o[44]||(o[44]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12l2 2 4-4"})],-1)]),8,zr),e("button",{onClick:V=>Z(i),class:"p-2 text-purple-400 hover:text-purple-300 hover:bg-purple-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Update Status"},o[45]||(o[45]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)]),8,Er),e("button",{onClick:V=>ee(i),class:"p-2 text-red-400 hover:text-red-300 hover:bg-red-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Delete Part"},o[46]||(o[46]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Hr)])])]))),128))])])]),g.parts.links?(a(),n("div",Fr,[e("div",Qr,[e("div",Rr,[g.parts.prev_page_url?(a(),F(s(z),{key:0,href:g.parts.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:I(()=>o[47]||(o[47]=[M(" Previous ",-1)])),_:1,__:[47]},8,["href"])):p("",!0),g.parts.next_page_url?(a(),F(s(z),{key:1,href:g.parts.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:I(()=>o[48]||(o[48]=[M(" Next ",-1)])),_:1,__:[48]},8,["href"])):p("",!0)]),e("div",Kr,[e("div",null,[e("p",Gr," Showing "+u(g.parts.from)+" to "+u(g.parts.to)+" of "+u(g.parts.total)+" results ",1)]),e("div",null,[e("nav",Jr,[(a(!0),n(B,null,N(g.parts.links,i=>(a(),n(B,{key:i.label},[i.url?(a(),F(s(z),{key:0,href:i.url,class:C(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":i.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!i.active}]),innerHTML:i.label},null,8,["href","class","innerHTML"])):(a(),n("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:i.label},null,8,Wr))],64))),128))])])])])])):p("",!0)])]),U(tt,{show:c.value,part:k.value,devices:g.devices,onClose:o[10]||(o[10]=i=>c.value=!1),onSaved:te},null,8,["show","part","devices"]),U($t,{show:_.value,part:f.value,onClose:o[11]||(o[11]=i=>_.value=!1),onSaved:re},null,8,["show","part"]),U(Rt,{show:t.value,part:x.value,onClose:o[12]||(o[12]=i=>t.value=!1),onSaved:se},null,8,["show","part"]),U(ve,{show:b.value,processing:r.value,title:"Delete Part",message:`Are you sure you want to delete ${l.value?.name}? This action cannot be undone.`,"confirm-text":"Delete Part","cancel-text":"Cancel",type:"danger",onConfirm:oe,onCancel:le},null,8,["show","processing","message"])]),_:1})],64))}};export{rs as default};
