import{r as P,m as f,g as l,o as n,a as d,d as g,h as z,w as p,b as e,t as i,i as B,F as u,y as v,n as M,l as T,j as L,I as S}from"./app-BGh5SFxi.js";import{_ as D}from"./AuthenticatedLayout-D7wPPUcl.js";import{C as _}from"./Chart-DPkq-W3M.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";import"./chart-C26Vmg0g.js";const F={class:"flex justify-between items-center"},R={class:"flex items-center space-x-4"},V={class:"flex items-center space-x-4"},N=["value"],A={class:"py-12"},G={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},O={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},I={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},U={class:"flex items-center"},E={class:"ml-4"},H={class:"text-2xl font-semibold text-white"},W={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},$={class:"flex items-center"},Y={class:"ml-4"},q={class:"text-2xl font-semibold text-white"},J={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},K={class:"flex items-center"},Q={class:"ml-4"},X={class:"text-2xl font-semibold text-white"},Z={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-semibold text-white"},oe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ie={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},ae={class:"p-6"},re={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},le={class:"p-6"},ne={class:"bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg"},de={class:"p-6"},ce={class:"overflow-x-auto"},me={class:"min-w-full divide-y divide-gray-700"},pe={class:"bg-gray-800 divide-y divide-gray-700"},ue={class:"px-6 py-4 whitespace-nowrap"},xe={class:"flex items-center"},he={class:"flex-shrink-0 h-10 w-10"},fe={class:"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"},ge={class:"text-sm font-medium text-white"},ve={class:"ml-4"},_e={class:"text-sm font-medium text-white"},ye={class:"text-sm text-gray-400"},we={class:"px-6 py-4 whitespace-nowrap"},be={class:"text-sm font-medium text-blue-400"},ke={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"text-sm text-gray-300"},je={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"text-sm text-gray-300"},ze={class:"px-6 py-4 whitespace-nowrap"},Be={class:"px-6 py-4 whitespace-nowrap"},Me={class:"text-sm text-gray-300"},Te={key:0},Ne={__name:"TechnicianPerformance",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(a){const r=a,x=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),c=s=>parseFloat(s||0).toLocaleString("en-US"),h=s=>`${parseFloat(s||0).toFixed(1)}%`,y=[{value:"7days",label:"Last 7 Days"},{value:"30days",label:"Last 30 Days"},{value:"90days",label:"Last 90 Days"},{value:"1year",label:"Last Year"}],m=P(r.period),w=()=>{window.location.href=route("reports.technician-performance",{period:m.value})},b=f(()=>({type:"bar",data:{labels:r.charts.technician_productivity?.map(s=>s.name)||[],datasets:[{label:"Repairs Completed",data:r.charts.technician_productivity?.map(s=>s.repairs_completed)||[],backgroundColor:"rgba(59, 130, 246, 0.8)",borderColor:"rgb(59, 130, 246)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}},scales:{x:{ticks:{color:"#9ca3af"}},y:{ticks:{color:"#9ca3af"}}}}})),k=f(()=>({type:"doughnut",data:{labels:r.charts.workload_distribution?.map(s=>s.name)||[],datasets:[{data:r.charts.workload_distribution?.map(s=>s.workload_percentage)||[],backgroundColor:["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4","#84cc16","#f97316"]}]},options:{responsive:!0,plugins:{legend:{labels:{color:"#e5e7eb"}}}}})),C=s=>s>=90?"bg-green-100 text-green-800":s>=80?"bg-blue-100 text-blue-800":s>=70?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",j=s=>s>=90?"Excellent":s>=80?"Good":s>=70?"Average":"Needs Improvement";return(s,t)=>(n(),l(u,null,[d(g(z),{title:"Technician Performance"}),d(D,null,{header:p(()=>[e("div",F,[e("div",R,[d(g(T),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:p(()=>t[1]||(t[1]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),t[2]||(t[2]=e("h2",{class:"font-semibold text-xl text-white leading-tight"}," Technician Performance ",-1))]),e("div",V,[L(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>m.value=o),onChange:w,class:"bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-red-500 focus:border-red-500 px-3 py-2"},[(n(),l(u,null,v(y,o=>e("option",{key:o.value,value:o.value},i(o.label),9,N)),64))],544),[[S,m.value]])])])]),default:p(()=>[e("div",A,[e("div",G,[e("div",O,[e("div",I,[e("div",U,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])])],-1)),e("div",E,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-gray-400"},"Active Technicians",-1)),e("p",H,i(c(a.metrics.total_technicians)),1)])])]),e("div",W,[e("div",$,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",Y,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-400"},"Avg. Productivity",-1)),e("p",q,i(h(a.metrics.average_productivity)),1)])])]),e("div",J,[e("div",K,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])])],-1)),e("div",Q,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-400"},"Total Repairs",-1)),e("p",X,i(c(a.metrics.total_repairs_completed)),1)])])]),e("div",Z,[e("div",ee,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),e("div",te,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-400"},"Revenue Generated",-1)),e("p",se,i(x(a.metrics.total_revenue_generated)),1)])])])]),e("div",oe,[e("div",ie,[e("div",ae,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Technician Productivity",-1)),d(_,{config:b.value},null,8,["config"])])]),e("div",re,[e("div",le,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-white mb-4"},"Workload Distribution",-1)),d(_,{config:k.value},null,8,["config"])])])]),e("div",ne,[e("div",de,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-white mb-6"},"Individual Performance",-1)),e("div",ce,[e("table",me,[t[14]||(t[14]=e("thead",{class:"bg-gray-700"},[e("tr",null,[e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Technician "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Repairs Completed "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Avg. Completion Time "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Revenue Generated "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Performance Rating "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"}," Specialization ")])],-1)),e("tbody",pe,[(n(!0),l(u,null,v(a.metrics.technician_details,o=>(n(),l("tr",{key:o.id,class:"hover:bg-gray-700"},[e("td",ue,[e("div",xe,[e("div",he,[e("div",fe,[e("span",ge,i(o.name?.charAt(0)||"T"),1)])]),e("div",ve,[e("div",_e,i(o.name),1),e("div",ye,i(o.email),1)])])]),e("td",we,[e("div",be,i(c(o.repairs_completed)),1)]),e("td",ke,[e("div",Ce,i(c(o.avg_completion_time))+" days",1)]),e("td",je,[e("div",Pe,i(x(o.revenue_generated)),1)]),e("td",ze,[e("span",{class:M(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",C(o.performance_rating)])},i(j(o.performance_rating))+" ("+i(h(o.performance_rating))+") ",3)]),e("td",Be,[e("div",Me,i(o.specialization||"General"),1)])]))),128)),!a.metrics.technician_details||a.metrics.technician_details.length===0?(n(),l("tr",Te,t[13]||(t[13]=[e("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-400"}," No technician data available for the selected period. ",-1)]))):B("",!0)])])])])])])])]),_:1})],64))}};export{Ne as default};
