import{_ as S}from"./AuthenticatedLayout-D7wPPUcl.js";import{r as _,p as D,g as d,o as n,a as g,d as m,h as C,w as c,b as t,t as o,i as B,F as x,y as p,l as f,n as w,f as b,c as M}from"./app-BGh5SFxi.js";import{C as k,r as V}from"./chart-C26Vmg0g.js";import"./ApplicationLogo-CxXkoeMW.js";import"./DropdownLink-CBw8P_6_.js";k.register(...V);const O={class:"flex items-center justify-between"},F={class:"text-sm text-gray-400 mt-1"},N={class:"py-12"},$={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},j={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},T={class:"flex items-center"},J={class:"ml-4"},L={class:"text-2xl font-bold text-white"},Y={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},z={class:"flex items-center"},E={class:"ml-4"},H={class:"text-2xl font-bold text-white"},I={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},P={class:"text-lg font-semibold text-white mb-4"},U={class:"h-96"},W={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},q={class:"overflow-x-auto"},G={class:"min-w-full divide-y divide-gray-700"},K={class:"divide-y divide-gray-700"},Q={class:"px-6 py-4 whitespace-nowrap"},X={class:"text-sm font-medium text-white"},Z={class:"px-6 py-4 whitespace-nowrap"},tt={class:"text-sm text-gray-300"},et={class:"px-6 py-4 whitespace-nowrap"},st={class:"text-sm font-medium text-green-400"},rt={class:"px-6 py-4 whitespace-nowrap"},ot={class:"text-sm text-gray-300"},at={key:0},lt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},nt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},dt={class:"space-y-2"},it={class:"space-y-2"},yt={__name:"SalesReport",props:{reportData:Object,period:String,year:Number,month:Number},setup(s){const i=s,v=_(null);let y=null;const h=a=>"₱"+parseFloat(a||0).toLocaleString("en-US",{minimumFractionDigits:2}),u=a=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][a-1],R=()=>{y&&y.destroy();const a=v.value.getContext("2d");let e,r;i.period==="monthly"?(e=i.reportData.dailySales.map(l=>l.day),r=i.reportData.dailySales.map(l=>l.revenue)):(e=i.reportData.monthlySales.map(l=>u(l.month)),r=i.reportData.monthlySales.map(l=>l.revenue)),y=new k(a,{type:"line",data:{labels:e,datasets:[{label:"Revenue",data:r,borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)",borderWidth:2,fill:!0,tension:.4}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#ffffff"}}},scales:{x:{ticks:{color:"#9ca3af"},grid:{color:"rgba(156, 163, 175, 0.1)"}},y:{ticks:{color:"#9ca3af",callback:function(l){return"₱"+l.toLocaleString()}},grid:{color:"rgba(156, 163, 175, 0.1)"}}}}})};return D(()=>{R()}),(a,e)=>(n(),d(x,null,[g(m(C),{title:`Sales Report - ${s.period==="monthly"?u(s.month)+" "+s.year:s.year}`},null,8,["title"]),g(S,null,{header:c(()=>[t("div",O,[t("div",null,[e[0]||(e[0]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Sales Report ",-1)),t("p",F,o(s.period==="monthly"?`${u(s.month)} ${s.year}`:`Year ${s.year}`),1)]),g(m(f),{href:a.route("reports.index"),class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"},{default:c(()=>e[1]||(e[1]=[b(" Back to Reports ",-1)])),_:1,__:[1]},8,["href"])])]),default:c(()=>[t("div",N,[t("div",$,[t("div",j,[t("div",A,[t("div",T,[e[3]||(e[3]=t("div",{class:"p-3 bg-green-900/50 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",J,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Revenue",-1)),t("p",L,o(h(s.reportData.totalRevenue)),1)])])]),t("div",Y,[t("div",z,[e[5]||(e[5]=t("div",{class:"p-3 bg-blue-900/50 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),t("div",E,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Orders",-1)),t("p",H,o(s.reportData.totalOrders),1)])])])]),t("div",I,[t("h3",P," Revenue Trend - "+o(s.period==="monthly"?"Daily":"Monthly"),1),t("div",U,[t("canvas",{ref_key:"chartRef",ref:v},null,512)])]),t("div",W,[e[8]||(e[8]=t("h3",{class:"text-lg font-semibold text-white mb-4"},"Service Revenue Breakdown",-1)),t("div",q,[t("table",G,[e[7]||(e[7]=t("thead",null,[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Service"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Orders"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Revenue"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg. Order Value")])],-1)),t("tbody",K,[(n(!0),d(x,null,p(s.reportData.serviceBreakdown,r=>(n(),d("tr",{key:r.name,class:"hover:bg-gray-750"},[t("td",Q,[t("div",X,o(r.name),1)]),t("td",Z,[t("div",tt,o(r.orders_count),1)]),t("td",et,[t("div",st,o(h(r.revenue)),1)]),t("td",rt,[t("div",ot,o(h(r.revenue/r.orders_count)),1)])]))),128)),s.reportData.serviceBreakdown.length===0?(n(),d("tr",at,e[6]||(e[6]=[t("td",{colspan:"4",class:"px-6 py-4 text-center text-gray-400"}," No service data available for this period ",-1)]))):B("",!0)])])])]),t("div",lt,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold text-white mb-4"},"View Other Periods",-1)),t("div",nt,[t("div",null,[e[9]||(e[9]=t("h4",{class:"text-sm font-medium text-gray-400 mb-2"},"Monthly Reports",-1)),t("div",dt,[(n(),d(x,null,p(12,r=>g(m(f),{key:r,href:a.route("reports.revenue"),class:w(["block px-3 py-2 rounded text-sm transition-colors duration-200",s.period==="monthly"&&s.month===r?"bg-red-600 text-white":"bg-gray-700 hover:bg-gray-600 text-gray-300"])},{default:c(()=>[b(o(u(r))+" "+o(s.year),1)]),_:2},1032,["href","class"])),64))])]),t("div",null,[e[10]||(e[10]=t("h4",{class:"text-sm font-medium text-gray-400 mb-2"},"Yearly Reports",-1)),t("div",it,[(n(!0),d(x,null,p([s.year-2,s.year-1,s.year,s.year+1],r=>(n(),M(m(f),{key:r,href:a.route("reports.orders"),class:w(["block px-3 py-2 rounded text-sm transition-colors duration-200",s.period==="yearly"&&s.year===r?"bg-red-600 text-white":"bg-gray-700 hover:bg-gray-600 text-gray-300"])},{default:c(()=>[b(" Year "+o(r),1)]),_:2},1032,["href","class"]))),128))])])])])])])]),_:1})],64))}};export{yt as default};
